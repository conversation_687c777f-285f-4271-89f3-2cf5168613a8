<?php declare(strict_types=1);

namespace App\PostType\Tag\Model\Checker\Task;

use App\Model\ElasticSearch\Product\Convertor\FlagData;
use App\Model\ElasticSearch\Product\ConvertorProvider;
use App\Model\ElasticSearch\Product\Facade;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Setup;
use App\PostType\Tag\Model\Checker\Checker;
use App\PostType\Tag\Model\Checker\CheckerDTO;
use App\PostType\Tag\Model\Orm\Tag\TagModel;
use App\PostType\Tag\Model\TagType;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class TagConsumer
{

	private ?ProductLocalization $productLocalization;

	private Mutation $mutation;

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly Facade $esProductFacade,
		private readonly Checker $checker,
		private readonly TagModel $tagModel,
		private readonly ConvertorProvider $convertorProvider,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	public function __invoke(TagMessage $message): void
	{
		$this->productLocalizationRepository->setPublicOnly(false);
		$this->productRepository->setPublicOnly(false);
		$this->productLocalization = $this->productLocalizationRepository->getById($message->productLocalizationId);

		if ($this->productLocalization === null) {
			return;
		}
		$this->mutation = $this->productLocalization->mutation;
		$this->mutationHolder->setMutation($this->mutation);

		$setup = new Setup(
			$this->mutation,
			$this->mutation->getFirstState(),
			$this->mutation->getDefaultPriceLevel(),
		);

		$hasTag = $this->checker->getCheckerFunction($message->tagType, $setup)(new CheckerDTO(productLocalization: $this->productLocalization));

		if ($hasTag) {
			$this->add($message->tagType);
		} else {
			$this->remove($message->tagType);
		}
		$this->esProductFacade->updateAllMutationsNow($this->productLocalization->product, [$this->convertorProvider->get(FlagData::class)]);
		$this->productLocalization->product->flushTemplateCache();
	}

	private function remove(TagType $tagType): void
	{
		$this->tagModel->updateByTagType($this->productLocalization->product, false, $tagType, mutation: $this->mutation);
		$this->productRepository->persistAndFlush($this->productLocalization->product);
	}

	private function add(TagType $tagType, ?DateTimeImmutable $from = null, ?DateTimeImmutable $to = null): void
	{
		$this->tagModel->updateByTagType($this->productLocalization->product, true, $tagType, mutation: $this->mutation);
		$this->productRepository->persistAndFlush($this->productLocalization->product);
	}

}

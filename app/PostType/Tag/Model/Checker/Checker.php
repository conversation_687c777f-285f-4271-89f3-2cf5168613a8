<?php

namespace App\PostType\Tag\Model\Checker;

use App\Model\Orm\ClassEvent\ClassEventModel;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Tag\Model\TagType;
use Closure;

class Checker
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly TagRepository $tagRepository,
		private readonly ClassEventModel $classEventModel,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
	)
	{
	}

	/**
	 * @phpstan-return  Closure(CheckerDTO $checkerDTO): bool
	 */
	public function getCheckerFunction(TagType $tagType, Setup $setup): Closure
	{
		return match ($tagType) {
			TagType::new => fn (CheckerDTO $checkerDTO): bool => $this->hasNew($checkerDTO),
			TagType::transitFree => fn (CheckerDTO $checkerDTO): bool => $this->hasTransitFree($checkerDTO, $setup),
			TagType::lastMinute => fn (CheckerDTO $checkerDTO): bool => $this->hasLastMinute($checkerDTO),
			TagType::paidByLo => fn (CheckerDTO $checkerDTO): bool => $this->hasPaidByLo($checkerDTO),
			TagType::paidByLoFull => fn (CheckerDTO $checkerDTO): bool => $this->hasPaidByLoFull($checkerDTO),
			TagType::giftFree => fn (CheckerDTO $checkerDTO): bool => $this->hasPresent($checkerDTO),
			TagType::promoPrice => fn (CheckerDTO $checkerDTO): bool => $this->hasPromoPrice($checkerDTO),
			TagType::videoReview => fn (CheckerDTO $checkerDTO): bool => $this->hasVideoReview($checkerDTO),
			default => fn (CheckerDTO $checkerDTO): bool => false,
		};
	}

	private function hasPromoPrice(CheckerDTO $checkerDTO): bool
	{
		$productLocalization = $checkerDTO->productLocalization;
		$mutation = $productLocalization->mutation;
		$priceLevelDefault = $this->priceLevelRepository->getDefault();
		$productLocalization->product->setMutation($mutation);

		return $productLocalization->product->hasPromoPrice($mutation, $priceLevelDefault, $mutation->states->toCollection()->fetch());
	}

	private function hasPaidByLo(CheckerDTO $checkerDTO): bool
	{
		$productLocalization = $checkerDTO->productLocalization;
		return $productLocalization->hasBuyableRequalificationPossibility($productLocalization->mutation->getFirstState());
	}

	private function hasPresent(CheckerDTO $checkerDTO): bool
	{
		$productLocalization = $checkerDTO->productLocalization;
		return $this->tagRepository->findProductsWithPresent()->getById($productLocalization->product->id) !== null;
	}

	private function hasPaidByLoFull(CheckerDTO $checkerDTO): bool
	{
		$productLocalization = $checkerDTO->productLocalization;
		return $productLocalization->hasRequalificationPossibility($productLocalization->mutation->getFirstState()) && $productLocalization->requalificationPossibility === 100;
	}

	private function hasTransitFree(CheckerDTO $checkerDTO, Setup $setup): bool
	{
		if ($checkerDTO->productLocalization->product->isCourse()) {
			return false;
		}

		$productLocalization = $checkerDTO->productLocalization;

		$minDeliveryPrice = $this->deliveryMethodConfigurationRepository->getTransitFreeFromLevel($productLocalization->mutation, $setup->state, $setup->priceLevel, $productLocalization->mutation->currency);
		$productVariant = $checkerDTO->productVariant;
		if ($productVariant instanceof ProductVariant) {
			$priceInfo = $productVariant->getPriceInfo($setup->mutation, $setup->priceLevel, $setup->state);
		} else {
			$priceInfo = null;
			foreach ($productLocalization->product->activeVariants as $activeVariant) {

				$variantPriceInfo = $activeVariant->getPriceInfo($setup->mutation, $setup->priceLevel, $setup->state);
				if ($priceInfo === null || $variantPriceInfo->getSellingPriceVat()->isGreaterThan($priceInfo->getSellingPriceVat())) {
					$priceInfo = $variantPriceInfo;
				}
			}

		}

		if ($priceInfo === null) {
			return false;
		}

		if ($minDeliveryPrice === null) {
			return false;
		}

		return $minDeliveryPrice <= $priceInfo->getSellingPriceVat()->getAmount()->toFloat();
	}

	private function hasNew(CheckerDTO $checkerDTO): bool
	{
		$productLocalization = $checkerDTO->productLocalization;
		return $this->productRepository->isNewProductByDateCreated($productLocalization->product);
	}

	private function hasLastMinute(CheckerDTO $checkerDTO): bool
	{
		$product = $checkerDTO->productLocalization->product;
		if (!$product->isCourse()) {
			return false;
		}

		$daysCount = (int) ($product->mainCategory->cf->lastMinute->daysCount ?? 0);
		$capacityCount = (int) ($product->mainCategory->cf->lastMinute->capacityCount ?? 0);

		foreach ($product->classEventsPublic as $classEvent) {
			$hasLastMinute = $this->classEventModel->hasLastMinuteOption($classEvent, $daysCount, $capacityCount);
			if ($hasLastMinute) {
				return true;
			}
		}

		return false;
	}

	private function hasVideoReview(CheckerDTO $checkerDTO): bool
	{
		return ($checkerDTO->productLocalization->product->cf->videos ?? []) !== [];
	}

}

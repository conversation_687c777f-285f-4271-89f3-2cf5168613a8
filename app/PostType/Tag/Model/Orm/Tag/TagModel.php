<?php declare(strict_types=1);

namespace App\PostType\Tag\Model\Orm\Tag;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Setup;
use App\PostType\Tag\Model\Checker\Checker;
use App\PostType\Tag\Model\Checker\CheckerDTO;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use App\PostType\Tag\Model\TagType;
use DateTimeImmutable;
use LogicException;
use Nextras\Orm\Exception\NoResultException;

final class TagModel
{

	use HasStaticCache;

	public function __construct(
		private readonly TagRepository $tagRepository,
		private readonly TagLocalizationRepository $tagLocalizationRepository,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		private readonly MutationsHolder $mutationsHolder,
		private readonly StateRepository $stateRepository,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly Checker $checker,
	)
	{
	}

	public function updateTag(Product $product, bool $enabled, Tag $tag, ?DateTimeImmutable $from = null, ?DateTimeImmutable $to = null, ?Mutation $mutation = null): void
	{
		$tag = $this->tagRepository->getBy(['id' => $tag->id]);
		if ($tag === null) {
			return;
		}

		if ($tag->type->isMutationDependTag()) {
			try {
				$tagLocalization = $tag->getLocalization($mutation);
			} catch (NoResultException) {
				return;
			}

			if ($enabled) {
				$this->tagLocalizationRepository->replaceRelationRow($product, $tagLocalization, $from, $to);
			} else {
				$this->tagLocalizationRepository->removeRelationRow($product, $tagLocalization);
			}
		} else {
			if ($enabled) {
				$this->tagRepository->replaceRelationRow($product, $tag, $from, $to);
			} else {
				$product->tags->remove($tag);
			}
		}
	}

	public function updateByTagType(Product $product, bool $enabled, TagType $tagType, ?DateTimeImmutable $from = null, ?DateTimeImmutable $to = null, ?Mutation $mutation = null): void
	{
		if ($tagType === TagType::custom) {
			throw new LogicException('Use method updateTag() for update Custom tag type relation');
		}
		$tag = $this->tagRepository->getByChecked(['type' => $tagType]);
		$this->updateTag($product, $enabled, $tag, $from, $to, $mutation);
	}


	public function checkFreeTransport(Product $product): bool
	{
		//TODO tohle cele predelat
		$csMutation = $this->mutationsHolder->getDefault();

		$defaultPriceLevel = $this->tryLoadCache('defaultPriceLevel', function () {
			return $this->priceLevelRepository->getDefault();
		});

		$csState = $this->tryLoadCache('defaultState', function () {
			return $this->stateRepository->getByChecked(['code' => State::DEFAULT_CODE]);
		});

		$price = $product->price($csMutation, $defaultPriceLevel, $csState);

		// todo add dimensions
		/*
		$values = $this->parameterValueRepository->getRawValues($product, Parameter::UID_WEIGHT);
		$weight = isset($values[0]) ? $values[0]->internalValue : null;
		if ($weight === null) {
			return false;
		}
		*/

		return $this->deliveryMethodConfigurationRepository->hasFreeTransportsForProduct($csMutation, $defaultPriceLevel, $csState, $price, 0);
	}


	/**
	 * IMPORTANT! - nepouzivat na FE
	 */
	public function checkTagsForProduct(Product $product): void
	{
		foreach ($product->productLocalizations as $productLocalization) {
			$product->setMutation($productLocalization->mutation);
			$mutation = $productLocalization->getMutation();
			$setup = new Setup(
				mutation: $mutation,
				state: $mutation->getFirstState(),
				priceLevel: $mutation->getDefaultPriceLevel(),
				userEntity: null
			);

			$tagTypes = [
				TagType::paidByLo,
				TagType::paidByLoFull,
				TagType::new,
				TagType::promoPrice,
				TagType::transitFree,
				TagType::videoReview,
			];

			foreach ($tagTypes as $tagType) {
				$status = $this->checker->getCheckerFunction($tagType, $setup)(new CheckerDTO(productLocalization: $productLocalization));
				$this->updateByTagType($productLocalization->product, $status, $tagType, mutation: $mutation);
			}
		}
	}

}

<?php

declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Presenters;

use App\Components\VisualPaginator\VisualPaginator;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\LikeDislike\HasLikeDislike;
use App\FrontModule\Components\LikeDislike\LikeDislike;
use App\FrontModule\Components\ProductsParsedFromPage\ProductsParsedFromPage;
use App\FrontModule\Components\ProductsParsedFromPage\ProductsParsedFromPageFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\ElasticSearch\Common\Repository;
use App\Model\Orm\Orm;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredData;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory;
use App\PostType\Blog\FrontModule\Components\Default\CategorySection\CategorySection;
use App\PostType\Blog\FrontModule\Components\Default\CategorySection\CategorySectionFactory;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Blog\FrontModule\Components\MostReadArticles\MostReadArticles;
use App\PostType\Blog\FrontModule\Components\MostReadArticles\MostReadArticlesFactory;
use App\PostType\Blog\FrontModule\Components\NewestArticles\NewstArticles;
use App\PostType\Blog\FrontModule\Components\NewestArticles\NewstArticlesFactory;
use App\PostType\Blog\FrontModule\Components\Default\NewestArticles\NewestArticles as DefaultNewestArticles;
use App\PostType\Blog\FrontModule\Components\Default\NewestArticles\NewestArticlesFactory as DefaultNewestArticlesFactory;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\BlogTagModel;
use Elastica\ResultSet;
use Nette\Application\BadRequestException;
use Nette\Utils\Paginator;
use Nextras\Orm\Collection\ICollection;

/**
 * @method Blog getObject()
 */
final class BlogPresenter extends BasePresenter
{

	use HasCustomContentRenderer;
	use HasLikeDislike;

	private array $blogLocalizationsByCategoryIds = [];
	private array $newestBlogLocalizationsByCategoryIds = [];
	private array $mainCategoryIds = [];
	private array $subCategoryIds = [];

	public function __construct(
		private BlogLocalizationModel $blogLocalizationModel,
		private MostReadArticlesFactory $mostReadArticlesFactory,
		private NewstArticlesFactory $newestArticlesFactory,
		private DefaultNewestArticlesFactory $defaultNewestArticlesFactory,
		private Repository $elasticSearchRepository,
		private ProductsParsedFromPageFactory $productsParsedFromPageFactory,
		private BlogLocalizationStructuredDataFactory $blogLocalizationStructuredDataFactory,
		private CategorySectionFactory $categorySectionFactory,

		public Orm $orm,
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

	// DEFAULT

	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function renderDefault(CommonTree $object): void
	{
		$blogCategories = $object->crossroad;

		$this->template
			->add('blogCategories', $blogCategories);
		$this->template->add('shorts', $this->orm->youtubeVideo->findBy(['isShort' => true])->orderBy('publishedAt', ICollection::DESC)->limitBy(20));
	}

	public function createComponentDefaultNewestArticles(): DefaultNewestArticles
	{
		return $this->defaultNewestArticlesFactory->create($this->mutation);
	}

	public function createComponentFirstCategorySection(): CategorySection
	{
		$items = $this->object->cf->firstSectionCategories ?? [];

		return $this->categorySectionFactory->create($this->mutation, $items, 3);
	}

	public function createComponentSecondCategorySection(): CategorySection
	{
		$items = $this->object->cf->secondSectionCategories ?? [];

		return $this->categorySectionFactory->create($this->mutation, $items, 2);
	}

	// CATEGORY

	public function actionCategory(CommonTree $object): void
	{
		$this->setObject($object);

		assert($this->object instanceof CommonTree);

		foreach ($this->object->pathItems as $pathItem) {
			if (! $pathItem?->isPublic()) {
				throw new BadRequestException();
			}
		}

		$blogLocalizationResultSet = $this->elasticSearchRepository->getBlogLocalizationsInCategory($this->mutation, $this->object);

		$this->setMainCategories($blogLocalizationResultSet);
		$this->setSubCategories($blogLocalizationResultSet);
		$this->setBlogLocalizationsByCategoryIds($blogLocalizationResultSet);
		$this->setNewestBlogLocalizationsByCategoryIds($blogLocalizationResultSet);

		if ($this->isAjax() && $this['pager']->getParameter('more') !== null) {
			$this->redrawControl('articlesPagerBottom');
			$this->redrawControl('articles');
		}
	}

	public function renderCategory(): void
	{
		$this->template
			->add('categories', $this->getCategories())
			->add('articles', $this->getPaginatedItems(12));
	}

	protected function createComponentMostReadArticles(): MostReadArticles
	{
		assert($this->object instanceof CommonTree);
		return $this->mostReadArticlesFactory->create($this->object, $this->mutation, $this->blogLocalizationsByCategoryIds);
	}

	protected function createComponentNewestArticles(): NewstArticles
	{
		assert($this->object instanceof CommonTree);
		return $this->newestArticlesFactory->create($this->object, $this->mutation, $this->newestBlogLocalizationsByCategoryIds);
	}

	protected function createComponentPager(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}

	/**
	 * @return ICollection<BlogLocalization>
	 */
	private function getPaginatedItems(int $itemsPerPage): ICollection
	{
		$blogLocalizations = $this->orm->blogLocalization->findBy([
				'mutation' => $this->mutation,
				'id' => $this->blogLocalizationsByCategoryIds,
				'id!=' => $this->newestBlogLocalizationsByCategoryIds,
			]);

		$totalItemsCount = $blogLocalizations->countStored();

		$paginator = $this->paginator($itemsPerPage, $totalItemsCount);

		return $blogLocalizations->limitBy($paginator->getLength(), $paginator->getOffset())
			->orderBy('publicFrom', 'DESC');
	}

	private function paginator(int $itemsPerPage, int $totalItemsCount): Paginator
	{
		$this['pager']->object  = $this->object;
		$this['pager']->special = true;
		$this['pager']->setTranslator($this->translator);

		$currentPage = (int) $this->getHttpRequest()->getQuery('pager-page');

		return $this['pager']
			->getPaginator()
			->setPage($currentPage)
			->setItemsPerPage($itemsPerPage)
			->setItemCount($totalItemsCount);
	}

	private function setMainCategories(?ResultSet $blogLocalizationResultSet): void
	{
		if ($blogLocalizationResultSet === null) {
			return;
		}
		$agregatedMainCategories = $blogLocalizationResultSet->getAggregation('mainCategories')['buckets'] ?? [];

		$this->mainCategoryIds = array_map(fn($item) => $item['key'], $agregatedMainCategories);
	}

	private function setSubCategories(?ResultSet $blogLocalizationResultSet): void
	{
		if ($blogLocalizationResultSet === null) {
			return;
		}
		$agregatedSubCategories = $blogLocalizationResultSet->getAggregation('subCategories')['buckets'] ?? [];

		$this->subCategoryIds = array_map(fn($item) => $item['key'], $agregatedSubCategories);
	}

	private function setBlogLocalizationsByCategoryIds(?ResultSet $blogLocalizationResultSet): void
	{
		if ($blogLocalizationResultSet === null) {
			return;
		}
		$agregatedBlogLocalizations = $blogLocalizationResultSet->getAggregation('inCategoryBlogs')['blogLocalizationIds']['buckets'] ?? [];

		$this->blogLocalizationsByCategoryIds = array_map(fn($item) => $item['key'], $agregatedBlogLocalizations);
	}

	private function setNewestBlogLocalizationsByCategoryIds(?ResultSet $blogLocalizationResultSet): void
	{
		if ($blogLocalizationResultSet === null) {
			return;
		}
		$agregatedNewestBlogLocalizations = $blogLocalizationResultSet->getAggregation('inCategoryBlogs')['newestBlogLocalizationIds']['buckets'] ?? [];

		$this->newestBlogLocalizationsByCategoryIds = array_map(fn($item) => $item['key'], $agregatedNewestBlogLocalizations);
	}

	private function getCategories(): array
	{
		assert($this->object instanceof CommonTree);
		$subCategory = $this->object->crossroad->findBy(['id' => $this->subCategoryIds])->fetchAll();

		if ($subCategory === []) {
			if ($this->object->parent !== null) {
				$subCategory = $this->object->parent->crossroad->findBy(['id' => $this->mainCategoryIds])->fetchAll();
			}
		}

		return $subCategory;
	}

	// DETAIL

	public function actionDetail(BlogLocalization $object): void
	{
		$this->setObject($object);

		assert($this->object instanceof BlogLocalization);
		$this->blogLocalizationModel->increaseViews($object);

		if ($this->object->getMainCategory() !== null) {
			$blogLocalizationResultSet = $this->elasticSearchRepository->getBlogLocalizationsInCategory($this->mutation, $this->object->getMainCategory());

			$this->setBlogLocalizationsByCategoryIds($blogLocalizationResultSet);
		}
	}

	public function renderDetail(): void
	{
		$this->template->blogLocalization = $this->object;
		$this->template->moreArticles = $this->getMoreArticles();
	}

	protected function createComponentLikeDislike(): LikeDislike
    {
		assert($this->object instanceof BlogLocalization);
        return $this->likeDislikeFactory->create($this->mutation, $this->object);
	}

	protected function createComponentProductsParsedFromPage(): ProductsParsedFromPage
	{
		assert($this->object instanceof BlogLocalization);
		return $this->productsParsedFromPageFactory->create($this->object, $this->setup);
	}

	protected function createComponentBlogLocalizationStructuredData(): BlogLocalizationStructuredData
	{
		assert($this->object instanceof BlogLocalization);
		return $this->blogLocalizationStructuredDataFactory->create($this->object, $this->mutation);
	}

	/**
	 * @return ICollection<BlogLocalization>
	 */
	private function getMoreArticles(): ICollection
	{
		assert($this->object instanceof BlogLocalization);
		return $this->orm->blogLocalization->findBy([
			'id' => $this->blogLocalizationsByCategoryIds,
			'id!=' => $this->object->id,
		])
		->orderBy('publicFrom', 'DESC')
		->limitBy(10);
	}

}

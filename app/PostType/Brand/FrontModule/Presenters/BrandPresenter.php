<?php declare(strict_types=1);

namespace App\PostType\Brand\FrontModule\Presenters;

use App\Components\VisualPaginator\PagerLimits;
use App\FrontModule\Components\Bestsellers\Bestsellers;
use App\FrontModule\Components\Bestsellers\BestsellersFactory;
use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsCallbackBuilder;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\MainCategories\MainCategories;
use App\FrontModule\Components\MainCategories\MainCategoriesFactory;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\Pageable;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Common\AggregationParser\AlphabetParser;
use App\Model\ElasticSearch\Product\Repository;
use App\PostType\Brand\FrontModule\Components\Alphabet\HasAlphabetComponent;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalization;
use App\PostType\Brand\Model\Orm\BrandLocalization\BrandLocalizationRepository;
use App\PostType\Page\Model\Orm\CommonTree;
use DateTimeImmutable;
use Nette\Application\Attributes\Persistent;
use Nette\Application\BadRequestException;
use Nette\Application\UI\Presenter;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\DI\Attributes\Inject;
use Nette\Utils\Paginator;
use stdClass;

/**
 * @property DefaultTemplate $template
 * @method BrandLocalization getObject()
 */
class BrandPresenter extends BasePresenter implements Pageable
{

	use HasCustomContentRenderer;
	use HasAlphabetComponent;

	#[Inject]
	public MainCategoriesFactory $mainCategoriesFactory;

	#[Inject]
	public BestsellersFactory $bestsellersFactory;

	#[Inject]
	public CatalogProductsFactory $catalogProductsFactory;

	#[Inject]
	public CatalogProductsCallbackBuilder $catalogProductsCallbackBuilder;

	#[Inject]
	public AlphabetParser $alphabetParser;

	#[Persistent]
	public int $page = 1;

	private array $filterParams;

	private mixed $cleanFilterParam;

	#[Persistent]
	public ?string $category = null;

	private BucketFilterBuilder $bucketFilterBuilder;

	private stdClass $filter;

	public ?string $currentLetter;

	public array $existedLetters;

	public function __construct(
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly BrandLocalizationRepository $brandLocalizationRepository,
		private readonly Repository $elasticProductRepository,
	)
	{
	}

	protected function startup(): void
	{
		parent::startup();

		$this->template->seolink = false;

		$filterParams = $this->getRequest()->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;
		
	}

	public function actionDefault(CommonTree $object, ?string $currentLetter = null): void
	{
		$this->setObject($object);
		$this->currentLetter = $currentLetter;

		$this->existedLetters = $this->elasticProductRepository->getBrandLocalizationsAlphabetFromProduct($this->mutation);

		if (!in_array($this->currentLetter, $this->existedLetters) && $this->currentLetter !== null) {
			$this->redirect($this->mutation->pages->brands, ['currentLetter' => null]);
		}
	}

	public function renderDefault(): void
	{
		if ($this->isAjax()) {
			$this->redrawControl('brands');
		}

		$this->template
			->add('brandLocalizationGroupsByAlphabet', $this->getBrandLocalizationGroupsByAlphabet())
			->add('existedLetters', $this->existedLetters)
			->add('currentLetter', $this->currentLetter);
	}

	public function actionDetail(BrandLocalization $object, array $filter, string $order = 'top'): void
	{
		$this->setObject($object);

		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, $order, $this->filterParams);
		$this->bucketFilterBuilder->setCategory($this->category);

		$this->filter = new stdClass();

		$this->filter = $this->bucketFilterBuilder->getFilter();
		$this->seoToolsFilter = $this->filter;
		$this->seoToolsSort = $this->bucketFilterBuilder->getSort();

		$this['seoTools']->setSort($this->seoToolsSort);
		$this['seoTools']->setFilter($this->seoToolsFilter);

		$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);
	}

	public function renderDetail(BrandLocalization $object, string $order = 'top'): void
	{
		$this->template
			->add('brandLocalization', $object)
			->add('cleanFilterParamForCrossroad', $this->cleanFilterParam)
			->add('cleanFilterParam', $this->cleanFilterParam)
			->add('sortingOptions', [SortCreator::TOP, SortCreator::CHEAPEST, SortCreator::EXPENSIVE])
			->add('filter', $this->filter)
			->add('catalogOrder', $order)
			->add('categories', [])
			->add('linkSeoPage', $this->mutation->pages->brands);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->getTemplate()->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}


	// public function createComponentMainCategories(): MainCategories
	// {
	// 	return $this->mainCategoriesFactory->create($this->getObject(), $this->filter->mainCategories ?? []);
	// }

	public function createComponentProductListRecommended(): ProductList
	{
		$productIds = $orderedIds = $cartIds = [];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();

			$productIds = $this->userModel->findRelatedOrderedProducts(
				productIds: array_merge($orderedIds, $this->lastVisitedProduct->getProductIds()),
				orderFrom: (new DateTimeImmutable())->modify('-1 year')
			);

		}
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_INTERESTED, $this->userEntity)
			->setIncludeProductIds(array_keys($productIds))
			->setOrderedProductIds($orderedIds)
			->setCartProductIds($cartIds)
			->setAppendBestselling()
			->setLimit(21);
	}



	// protected function createComponentBestsellers(): Bestsellers
	// {
	// 	$bestsellerCount = (int) ($this->object->mutation->pages->writers->customFieldsJson->postTypeOtherSettings[0]->topProducts ?? 4);
	// 	return $this->bestsellersFactory->create($this->setup, $this->bucketFilterBuilder, $bestsellerCount);
	// }

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' => $this->cleanFilterParam,
		];

		$findCatalogProductsDataCallback = $this->catalogProductsCallbackBuilder->build($this->bucketFilterBuilder);

		return $this->catalogProductsFactory->create(
			$this->object,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
			'catalog',
			'brand',
		);
	}

	public function getPagerLimits(): ?PagerLimits
	{
		assert($this instanceof Presenter);
		if ($this->action === 'default') {
			return null;
		}

		$paginator = $this['catalogProducts']['pager']->getPaginator();
		assert($paginator instanceof Paginator);

		return new PagerLimits($paginator->page, $paginator->pageCount);
	}

	private function getBrandLocalizationGroupsByAlphabet(): array
	{
		$brandLocalizations = $this->brandLocalizationRepository->findBy([
			'mutation' => $this->mutation,
		]);

		if ($this->currentLetter !== null) {
			$brandLocalizations = $brandLocalizations->findBy([
				'alphabetGroup' => $this->currentLetter,
			]);
		}

		$groups = [];
		foreach ($brandLocalizations as $brandLocalization) {
			if ($brandLocalization->productsCount > 0) {
				$groups[$brandLocalization->alphabetGroup][] = $brandLocalization;
			}
		}

		$groups = $this->sortAlphabetGroups($groups);

		$groups = $this->sortAlphabetKeys($groups);

		return $groups;
	}

	private function sortAlphabetGroups(array $groups): array
	{
		return array_map(function (array $items): array {
			usort($items, fn($a, $b) => strnatcasecmp($a->name, $b->name));
			return $items;
		}, $groups);
	}

	private function sortAlphabetKeys(array $groups): array
	{
		uksort($groups, function ($a, $b) {
			return $this->customSort($a, $b);
		});

		return $groups;
	}

	private function customSort(mixed $a, mixed $b): int {
		$getType = function ($value): int {
			$value = (string)$value;
			if (preg_match('/^\p{L}+$/u', $value)) {
				return ctype_alpha($value) ? 0 : 1; // Characters
			}
			if (is_numeric($value)) {
				return 2; // Numbers
			}
			return 3; // Others
		};

		// Normalization chars
		$collator = new \Collator($this->mutation->isoCode);

		// Compare against type
		$typeA = $getType($a);
		$typeB = $getType($b);

		if ($typeA !== $typeB) {
			return $typeA <=> $typeB;
		}

		// If type is same, sort
		if ($typeA === 0) { // chars
			// First without accents, next with accents
			$normalizedA = $collator->getSortKey((string)$a);
			$normalizedB = $collator->getSortKey((string)$b);

			return $normalizedA <=> $normalizedB;
		}

		if ($typeA === 1) { // Numeric chars
			return $a <=> $b;
		}

		// Other characters
		return strcmp((string)$a, (string)$b);
	}

}

<?php
declare(strict_types = 1);

namespace App\Model;


use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\ProductVariant\ProductVariant;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Brick\Money\Money;

final class PriceInfo {

	private ?string $tag = null;
	private ?float $discountPercentage = null;
	private ?float $discountPercentageSort = null;
	private ?Money $discountMoney = null;
	private ?Money $originalPrice = null;

	private ?string $type = null;

	private Money $sellingPrice;
	private Money $sellingPriceWithoutVat;

	private Money $standardPrice;
	private Money $standardPriceVat;

	private Money $recommendedPrice;
	private Money $recommendedPriceVat;

	private Money $referencePrice;
	private Money $referencePriceVat;

	private ?Money $discountPrice;
	private ?Money $discountPriceVat;

	private bool $hasPriceFrom = false;


	public function __construct(
		ProductVariant $productVariant,
		Mutation $mutation,
		public readonly BigDecimal $vatRate,
		private readonly string $currency,
		Money $standardPrice,
		Money $recommendedPrice,
		Money $referencePrice,
		private readonly array $discountPricePeriod,
		?Money $discountPrice,
		public readonly ?string $priceBox = null,
	)
	{
		$this->standardPrice = $standardPrice;
		$this->standardPriceVat = $this->convertToPriceWithVat($standardPrice);

		$this->recommendedPrice = $recommendedPrice;
		$this->recommendedPriceVat = $this->convertToPriceWithVat($recommendedPrice);

		$this->referencePrice = $referencePrice;
		$this->referencePriceVat = $this->convertToPriceWithVat($referencePrice);

		$this->discountPrice = $discountPrice;
		$this->discountPriceVat = $this->convertToPriceWithVat($discountPrice);

		$this->init($productVariant, $mutation);
	}

	private function init(ProductVariant $variant, Mutation $mutation): void
	{
		$this->tag = $variant->product->getPriceTag($mutation);
		$this->sellingPriceWithoutVat = $this->discountPrice ?? $this->standardPrice;
		$this->sellingPrice = $this->discountPriceVat ?? $this->standardPriceVat;
		$diffPercentage = $this->getDifferencePercentage($this->referencePriceVat, $this->sellingPrice);

		$this->discountPercentageSort = $diffPercentage;

		$this->type = 'A';
		$this->originalPrice = $this->referencePriceVat;
		$this->discountPercentage = $this->getDifferencePercentage($this->referencePriceVat, $this->sellingPrice);
		$this->discountMoney = Money::zero($this->currency);
	}


	private function getDifferencePercentage(Money $price, Money $toDiff): float
	{
		if ($price->isZero()) {
			return 0;
		}
		return round(100 * ($price->getAmount()->toFloat() - $toDiff->getAmount()->toFloat()) / $price->getAmount()->toFloat(), 2);
	}

	private function hasDiscountTag(): bool
	{
		return $this->tag !== null;
	}

	public function getTag(): ?string
	{
		return $this->hasDiscountTag() ? $this->tag : null;
	}

	public function getOriginalPrice(): ?Money
	{
		return $this->originalPrice;
	}

	public function getDiscountPrice(): ?Money
	{
		return $this->discountPrice;
	}

	public function getDiscountPriceVat(): ?Money
	{
		return $this->discountPriceVat;
	}


	public function getDiscountPercentage(): ?float
	{
		if ($this->discountPercentage !== null) {
			return round($this->discountPercentage);
		}
		return null;
	}

	public function getDiscountPercentageSort(): ?float
	{
		if ($this->discountPercentageSort !== null) {
			return round($this->discountPercentageSort);
		}
		return null;
	}

	public function getDiscountAmount(): ?Money
	{
		return $this->discountMoney;
	}

	public function getDiscountDelta(): ?Money
	{
		if ($this->getOriginalPrice() !== null) {
			if($this->discountPercentage !== null && $this->originalPrice !== null){
				return $this->originalPrice->minus($this->standardPriceVat);
			}

			if(($discountAmount = $this->getDiscountAmount()) !== null){
				return $discountAmount;
			}
		}
		return null;
	}

	public function getType(): ?string
	{
		return $this->type;
	}

	public function getCurrencyCode(): string
	{
		return $this->currency;
	}

	public function getRecommendedPriceVat(): Money
	{
		return $this->recommendedPriceVat;
	}

	public function getRecommendedPrice(): Money
	{
		return $this->recommendedPrice;
	}

	public function getReferencePriceVat(): Money
	{
		return $this->referencePriceVat;
	}

	public function getReferencePrice(): Money
	{
		return $this->referencePrice;
	}

	public function getSellingPriceVat(): Money
	{
		return $this->sellingPrice;
	}

	public function getSellingPrice(): Money
	{
		return $this->sellingPriceWithoutVat;
	}

	public function getStandardPriceVat(): Money
	{
		return $this->standardPriceVat;
	}

	public function getStandardPrice(): Money
	{
		return $this->standardPrice;
	}

	public function getVatRate(): BigDecimal
	{
		return $this->vatRate;
	}

	public function getOldPrice(): ?Money
	{
		if ($this->discountPriceVat !== null && $this->discountPriceVat->isLessThan($this->standardPriceVat->getAmount())) {
			return $this->getStandardPriceVat();
		}
		return null;
	}

	public function getOldPriceDiscountPercentage(): ?int
	{
		$oldPrice = $this->getOldPrice()?->getAmount();
		if ($oldPrice=== null) {
			return null;
		}
		$price = $this->getSellingPriceVat()->getAmount();

		$discount = $oldPrice->minus($price)
		                     ->dividedBy($oldPrice, 2, RoundingMode::HALF_UP)
		                     ->multipliedBy(100);

		return $discount->toInt();
	}

	public function hasDiscount(): bool
	{
		return $this->getOldPriceDiscountPercentage() !== null;
	}


	public function convertToPriceWithVat(?Money $priceWithoutVat): ?Money
	{
		if ($priceWithoutVat === null) {
			return null;
		}

		return Price::from(VatCalculator::priceWithVat($priceWithoutVat, $this->vatRate))->asMoney();
	}


	public function setHasPriceFrom(bool $hasPriceFrom): void
	{
		$this->hasPriceFrom = $hasPriceFrom;
	}


	public function hasPriceFrom(): bool
	{
		return $this->hasPriceFrom;
	}

	public function getDiscountDate(): array
	{
		return $this->discountPricePeriod;
	}

}

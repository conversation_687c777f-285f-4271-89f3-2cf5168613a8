<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationModel;

class FulltextData implements Convertor
{

	public const COMMAND_SHORTCUT = 't';

	public function __construct(
		private readonly TagLocalizationModel $tagLocalizationModel,
	)
	{
	}

	public function convert(Product $product): array
	{
		$mutation = $product->getMutation();
		$productLocalization = $product->getLocalization($mutation);

//			$firstVariant = $productLocalization->getFirstActiveVariantByMutation($mutation);
		$texts = ($productLocalization->content !== null) ? strip_tags($productLocalization->content) : '';

		return [
			'productId' => $product->getPersistedId(),

			'fulltext-name' => $productLocalization->name,
			'fulltext-content' => $productLocalization->annotation . ' ' . $texts,
			'fulltext-categories' => $product?->mainCategory->name ?? '',
			'fulltext-tags' => array_map(fn(TagLocalization $localization) => $localization->name, $this->tagLocalizationModel->findByProduct($product, $mutation)->fetchAll()),
		];
	}

}

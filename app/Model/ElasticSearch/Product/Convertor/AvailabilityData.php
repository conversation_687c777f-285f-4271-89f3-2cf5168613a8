<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\SetupFactory;

class AvailabilityData implements Convertor
{

	public function __construct(private readonly SetupFactory $setupFactory)
	{
	}

	public function convert(Product $product): array
	{
		$data = [];
		// TODO: by mutation
		$data['availability'] = $this->getAvailabilities($product);

		return $data;
	}

	private function getAvailabilities(Product $product): string
	{
		$this->setupFactory->init([$product->productAvailability]);
		return $product->productAvailability->getType();
	}
}

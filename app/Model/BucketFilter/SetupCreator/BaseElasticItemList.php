<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\MultiValue;
use App\Model\BucketFilter\ElasticItem\QuestionableElasticItem;
use App\Model\BucketFilter\ElasticItem\Range;
use App\Model\BucketFilter\ElasticItem\SingleValue;
use App\Model\BucketFilter\QueryFilter;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasCache;
use App\Model\Time\CurrentDateTimeProvider;
use Elastica\QueryBuilder;


abstract class BaseElasticItemList implements ElasticItemListGenerator
{
	use HasCache;

	protected array $list = [];

	protected Routable $parameterSetupObject;

	public function __construct(
		protected readonly Routable $parameterObject,
		protected readonly State $currentState,
		protected readonly PriceLevel $priceLevel,
		protected readonly array $allSelectedParameters,
		protected readonly FilterFlagsConfig $elasticItemConfig,
		protected readonly QueryFilter $queryFilter,
		protected readonly CatalogParameter $catalogParameter,
		protected readonly ParameterValueFilterHelper $parameterValueFilterHelper,
		protected readonly CurrentDateTimeProvider $currentDateTimeProvider,
		protected readonly Mutation $mutation,
	)
	{
		// @phpstan-ignore-next-line
		$this->elasticItemConfig->setProductSliderCallback('price', function (Range $item) {
			$item->setElasticPath(['statePricesWithVat', $this->currentState->code, $this->priceLevel->type, $this->mutation->getSelectedCurrency()->getCurrencyCode()]);
		});
		$this->parameterSetupObject = $this->parameterObject;
		$this->init();
	}

	protected function init():void
	{

	}

	public function getElasticItemList(): array
	{
		return $this->getElasticItemListCustom();
	}

	protected function getElasticItemListDefault(): array
	{
		$this->createDefaultList();
		$this->createCustomList();
		return $this->list;
	}

	protected function getElasticItemListCustom(): array
	{
		$cfSpecialFiltersSetup = $this->catalogParameter->getParametersCfForSpecialFilter($this->parameterSetupObject);

		$this->list    = [];
		$pricePosition = (int) ($cfSpecialFiltersSetup->price->sort ?? 2);
		$flagsPosition = (int) ($cfSpecialFiltersSetup->showOnly->sort ?? 1);
		$flagsKeys     = array_keys($this->elasticItemConfig->getProductFlags());

		$this->createCustomList();
		$items = [];
		foreach ($this->list as $index => $item) {
			$items[($index+2) * 100] = $item;
		}

		$this->list = [];
		$this->createDefaultList();

		$flagIteration = 0;
		foreach ($this->list as $item) {
			$isFlags = in_array($item->getElasticKey(), $flagsKeys);
			$isPrice = $item->getElasticKey() === 'price';
			if ($isFlags) {
				$items[($flagsPosition * 100)-90+$flagIteration] = $item;
				$flagIteration++;
			} elseif ($isPrice) {
				$items[($pricePosition * 100)-50] = $item;
			}
		}

		ksort($items);
		return array_values($items);
	}

	protected function createDefaultList():void
	{
		$this->list = [];
		foreach ($this->elasticItemConfig->getProductFlags() as $name => $callback){
			$item = new SingleValue(
				$name,
				$this->allSelectedParameters[DiscreteValues::NAMESPACE_FLAGS][$name] ?? [],
				$this->queryFilter,
				fn($value) => (bool) $value,
			);
			if(is_callable($callback)){
				$callback($item);
			}

			$this->add($item);
		}

		foreach ($this->elasticItemConfig->getProductSliders() as $name => $callback){

			$conditionMust = $this->elasticItemConfig->getConditionMust($name);
			$aggregationFilterMust = $this->elasticItemConfig->getAggregationFilterMust($name);

			$item = new Range(
				$this->queryFilter,
				$name,
				(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['min'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['min'] : null,
				(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['max'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$name]['max'] : null,
				conditionMust: $conditionMust,
				aggregationFilterMust: $aggregationFilterMust,
			);

			if(is_callable($callback)){
				$callback($item);
			}

			$this->add($item);
		}
	}
	abstract protected function createCustomList():void;

	protected function add( QuestionableElasticItem $item): void
	{
		$this->list[] = $item;
	}

	protected function getItemsByCfSetup(): array
	{
		return $this->loadCache($this->createCacheKey('cfItems'), function () {
			$filterItems = [];

			$cfSetup            = $this->catalogParameter->getParametersCfForFilter($this->parameterSetupObject);
			$numericAsSliderIds = [];
			$parameterCustomFields = [];
			if ($cfSetup && isset($cfSetup->visibleParameters)) {
				foreach ($cfSetup->visibleParameters as $visibleParameter) {
					if (isset($visibleParameter->parameter) && $visibleParameter->parameter->getEntity()) {
						$parameterCustomFields[$visibleParameter->parameter->id] = $visibleParameter;
						if (isset($visibleParameter->numberAsRange) && $visibleParameter->numberAsRange) {
							$numericAsSliderIds[] = $visibleParameter->parameter->id;
						}
					}
				}
			}

			$esParameters = $this->catalogParameter->getPossibleParametersForCatalog($this->parameterSetupObject);
			foreach ($esParameters as $esParameter) {
				$aggregationQuery = $this->queryFilter;
				$searchIds = null;
				if (isset($this->allSelectedParameters[DiscreteValues::NAMESPACE_SEARCH][$esParameter->uid])) {
					$searchIds = $this->parameterValueFilterHelper->getIdsBySearch($esParameter, $this->allSelectedParameters[DiscreteValues::NAMESPACE_SEARCH][$esParameter->uid]);
				}

				if ($esParameter->type === Parameter::TYPE_MULTISELECT) {
					$filterItems[] = new MultiValue(
						elasticKey: $esParameter->uid,
						selectedValues: $this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [],
						//getOptionFunction: $this->parameterValueFilterHelper->getFunctionForValues($esParameter, $limit),
						queryFilter: $aggregationQuery,
						typeInElasticEnforcer: fn($value) => (int) $value,
						onlyIds: $searchIds,
						nestedValues: true,
					);
				} elseif ($esParameter->type === Parameter::TYPE_SELECT) {
					$filterItems[] = new SingleValue(
						elasticKey: $esParameter->uid,
						selectedValues: $this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [],
						queryFilter: $aggregationQuery,
						onlyIds: $searchIds,
						nestedValues: true,
					);
				} elseif ($esParameter->type === Parameter::TYPE_NUMBER) {
					if (in_array($esParameter->id, $numericAsSliderIds)) {
						// add range for numbers
						$filterItems[] = new Range($aggregationQuery, $esParameter->uid,
							(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['min'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['min'] : null,
							(isset($this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['max'])) ? (float) $this->allSelectedParameters[Range::NAMESPACE_RANGES][$esParameter->uid]['max'] : null,);
					} else {
						$filterItems[] = new SingleValue($esParameter->uid,
							$this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][$esParameter->uid] ?? [],
							$aggregationQuery,);
					}
				}
			}
			return $filterItems;
		});
	}
}

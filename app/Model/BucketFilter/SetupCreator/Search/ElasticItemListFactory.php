<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Search;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\CommonTree;

interface ElasticItemListFactory
{

	public function create(
		CommonTree $parameterObject,
		State $currentState,
		PriceLevel $priceLevel,
		array $allSelectedParameters,
		Mutation $mutation
	): ElasticItemList;

}

<?php 

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\Model\BucketFilter\BucketFilter;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\SetupCreator\Catalog;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\CatalogTree;

class ProductBucketFilterCreator
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly Catalog\BasicElasticItemListFactory $basicElasticItemListFactory,
		private readonly Catalog\ElasticItemListFactory $elasticItemListFactory,
		private readonly Catalog\BoxListFactory $boxListFactory,
	)
	{
	}


	public function getBucketListFilter(CatalogTree $object, array $filterParams): BucketFilter
	{
		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($object);

		$elasticItemListGenerator = $this->elasticItemListFactory->create(
			$object,
			$this->mutation->getFirstState(),
			$this->mutation->getDefaultPriceLevel(),
			$filterParams,
			$this->mutation,
		);

		$boxListGenerator = $this->boxListFactory->create($object, $filterParams);

		$bucketFilter = $this->bucketFilterFactory->create(
			$basicElasticItemListGenerator,
			$elasticItemListGenerator,
			$boxListGenerator,
			$this->mutation,
		);

		return $bucketFilter;
	}

}

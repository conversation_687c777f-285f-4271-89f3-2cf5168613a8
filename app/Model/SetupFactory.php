<?php

namespace App\Model;

use App\Exceptions\LogicException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Setupable;
use App\Model\Orm\State\State;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\User\UserProvider;

final readonly class SetupFactory
{
	public function __construct(
		private Orm $orm,
		private UserProvider $userProvider
	)
	{
	}

	public function create(?Mutation $mutation = null, ?State $state = null, ?PriceLevel $priceLevel = null): Setup
	{

		$mutation = $mutation ?? $this->orm->mutation->getDefault();
		$state = $state ?? $mutation->getFirstState();
		$priceLevel = $priceLevel ?? $mutation->getDefaultPriceLevel();

		return new Setup(
			$mutation,
			$state,
			$priceLevel,
			$this->userProvider->userEntity,
		);
	}

	public function init(array $services, ?Mutation $mutation = null, ?State $state = null, ?PriceLevel $priceLevel = null): Setup
	{
		$setup = $this->create($mutation, $state, $priceLevel);
		/** @var Setupable $service */
		foreach ($services as $service) {
			if (!($service instanceof Setupable)) {
				throw new LogicException(sprintf('Service %s does not implement "%s" interface', get_class($service), Setupable::class));
			}
			$service->setSetup($setup);
		}
		return $setup;
	}

}

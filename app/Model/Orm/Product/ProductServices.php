<?php

namespace App\Model\Orm\Product;

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\ClassEvent\ClassEventRepository;
use App\Model\Orm\LibraryImage\FlagImageService;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\ProductVariant\Availability\ProductAvailabilityService;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\Supply\SupplyRepository;
use App\Model\Orm\User\UserProvider;
use App\Model\Price\ProductPriceModel;
use App\Model\Template\PartCondition;
use App\Model\Time\CurrentDateTimeProvider;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalizationRepository;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;

readonly class ProductServices
{
	public PriceLevel $requalificationPriceLevel;
	public function __construct(
		public TreeRepository $treeRepository,
		public ParameterRepository $parameterRepository,
		public SupplyRepository $supplyRepository,
		public ProductRepository $productRepository,
		public MutationHolder $mutationHolder,
		public CatalogTreeModel $catalogTreeModel,
		public StateModel $stateModel,
		public PriceLevelModel $priceLevelModel,
		public ProductAvailabilityService $productAvailabilityService,
		public TagRepository $tagRepository,
		public TagLocalizationRepository $tagLocalizationRepository,
		public GiftLocalizationRepository $giftLocalizationRepository,
		public ParameterValueRepository $parameterValueRepository,
		public FlagImageService $flagImageService,
		public PartCondition $partCondition,
		public ProductPriceModel $productPriceModel,
		public UserProvider $userProvider,
		public CurrentDateTimeProvider $currentDateTimeProvider,
		public ClassEventRepository $classEventRepository,
		PriceLevelRepository $priceLevelRepository,
	)
	{
		$this->requalificationPriceLevel = $priceLevelRepository->getBy(['type' => PriceLevel::TYPE_REQUALIFICATION]);
	}
}

<?php

declare(strict_types=1);

namespace App\Model\Orm\Product;

use App\Exceptions\LogicException;
use App\Model\Mutation\Exceptions\MutationIsNotAttachedException;
use App\Model\Mutation\Exceptions\MutationNotSetException;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassSection\ClassSection;
use App\Model\Orm\CurrencyContainer; // phpcs:ignore
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\LibraryImage\FlagImageService;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\ProductVariant\Availability\ProductAvailability;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Review\Review;
use App\Model\Orm\State\State;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Supply\Supply;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Orm\Traits\HasParameters;
use App\Model\Orm\Traits\HasTemplateCache;
use App\Model\Orm\TreeProduct\TreeProduct;
use App\Model\PriceInfo;
use App\Model\Template\PartCondition;
use App\Model\VatCalculator;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Discount\Model\Orm\Discount\Discount;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Promotion\Model\Orm\Promotion\Promotion;
use App\PostType\Tag\Model\Orm\Tag\Tag;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\PostType\Tag\Model\TagType;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ArrayCollection;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\IRelationshipCollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use Nextras\Orm\Repository\IRepository;
use App\PostType\Rating\Model\Orm\RatingLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use Carbon\CarbonImmutable;
use Tracy\Debugger;

// phpcs:ignore
// phpcs:ignore

/**
 * @property int $id {primary}
 * @property int|null $legacyId {default null}
 * @property string|null $itemType {default null}
 * @property string|null $internalName
 * @property string $template {default ':Front:Product:detail'}
 * @property DateTimeImmutable $dateCreated {default now}
 * @property DateTimeImmutable|null $syncTime {default null}
 * @property string|null $syncChecksum {default null}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property string|null $uid
 * @property int|null $hideFirstImage {default 0}
 * @property int|null $soldCount {default 0}
 * @property int|null $notSoldSeparately {default 0}
 * @property int|null $discount
 * @property int|null $discountType
 * @property int|null $isSet
 * @property int|null $isOld {default 0}
 * @property int|null $isInPrepare {default 0}
 * @property int|null $boostScore {default null}
 * @property int $erpScore {default 0}
 * @property int $erpHeurekaPopularity {default 0}
 * @property int|null $categoryMainPosition {default null}
 * @property DateTimeImmutable|null $boostScoreValid {default null}
 * @property int $isElectronic {default 0}
 * @property int $isStockNull {default 0}
 * @property int $isAudiobook {virtual}
 * @property int $isBook {virtual}
 * @property int $isElectonicBook {virtual}
 * @property int $isDamaged {default 0}
 * @property string|null $damagedType {default null}
 * @property int|null $isFreeTransport {default 0}
 * @property int|null $isFreeTransportForced {default 0}
 * @property DateTimeImmutable|null $freeTransportForcedFrom
 * @property DateTimeImmutable|null $freeTransportForcedTo
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property float $reviewAverage {default 0}
 * @property float $score {default 0}
 * @property string $availability {enum self::AVAILABILITY_*} {default self::AVAILABILITY_}
 * @property array $vats {container JsonContainer}
 * @property string|null $extId
 * @property int $deleted {default 0}
 * @property DateTimeImmutable|null $giftDate {default null}
 * @property DateTimeImmutable|null $similarBuyProductsCalculateAt {default null}
 * @property ArrayHash $similarBuyProducts {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<ProductReview> $reviews {1:m ProductReview::$product, orderBy=[date=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ProductVariant> $variants {1:m ProductVariant::$product, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ProductImage> $images {1:m ProductImage::$product, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ProductTree> $productTrees {1:m ProductTree::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ProductLocalization> $productLocalizations {1:m ProductLocalization::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property ManyHasMany<ParameterValue> $parametersValues {m:m ParameterValue::$products, isMain=true, orderBy=[parameterSort=ASC, sort=ASC]}
 * @property ManyHasMany<Review> $magazineReviews {m:m Review::$products}
 *
 * @property OneHasMany<ClassSection> $classSections {1:m ClassSection::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ClassEvent> $classEvents {1:m ClassEvent::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 *
 * @property OneHasMany<TreeProduct> $treeProducts {1:m TreeProduct::$product, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ProductProduct> $productProducts {1:m ProductProduct::$mainProduct, cascade=[persist, remove]}
 * @property OneHasMany<ProductProduct> $attachedProducts {1:m ProductProduct::$attachedProduct, cascade=[persist, remove]}
 * @property ManyHasMany<Discount> $discounts {m:m Discount::$products}
 * @property ManyHasMany<Tag> $tags {m:m Tag::$products}
 * @property ManyHasMany<TagLocalization> $tagLocalizations {m:m TagLocalization::$products}
 * @property ManyHasMany<BlogLocalization> $blogLocalizations {m:m BlogLocalization::$products}
 *
 * @property Product|null $damagedParent {m:1 Product::$damagedProducts}
 * @property OneHasMany<Product> $damagedProducts {1:m Product::$damagedParent}
 * @property ProductType|null $productType {1:1 ProductType, isMain=true, oneSided=true}
 *
 * @property ManyHasMany<Promotion> $promotions {m:m Promotion::$products}
 *
 * VIRTUALS
 *
 *
 * @property int $public {virtual}
 * @property-read string|null $name {virtual}
 * @property-read string|null $annotation {virtual}
 * @property-read string|null $nameTitle {virtual}
 * @property-read string|null $nameAnchor {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string|null $keywords {virtual}
 * @property-read string|null $content {virtual}
 * @property-read string $nameVariant {virtual}
 *
 * @property-read ICollection<CatalogTree> $inCategories {virtual}
 *
 * @property ProductVariant|null $firstVariant {virtual}
 * @property ArrayHash|null $cf {virtual}
 *
 * @property-read ICollection<Tree> $pages {virtual}
 * @property-read ICollection<Tree> $pagesAll {virtual}
 *
 * @property-read ICollection<CatalogTree> $attachCategories {virtual}
 * @property-read ICollection<CatalogTree> $attachCategoriesAll {virtual}
 *
 * @property-read ICollection<Product> $accessories {virtual}
 * @property-read ICollection<Product> $accessoriesAll {virtual}
 *
 * @property-read ICollection<Product> $similarProducts {virtual}
 * @property-read ICollection<Product> $similarProductsAll {virtual}
 * @property-read ICollection<Product> $presents {virtual}
 * @property-read ICollection<Product> $presentsAll {virtual}
 *
 * @property-read ICollection<Product> $products {virtual}
 * @property-read ICollection<Product> $productsAll {virtual}
 *
 * @property ProductImage|null $firstImage {virtual}
 * @property array $reviewInfo {virtual}
 * @property OneHasMany<ProductFile> $files {virtual}
 * @property-read ICollection<Parameter> $variantParameters {virtual}
 * @property-read bool $hasShellVariant {virtual}
 * @property-read array|null $path {virtual}
 * @property-read ICollection<Supply> $supplies {virtual}
 * @property-read ICollection<ProductVariant> $activeVariants {virtual}
 * @property-read bool $isInStock {virtual} true = skladem alespon jedna varianta
 * @property-read int $totalSupplyCount {virtual}
 * @property-read int $suplyCountStockDefault {virtual}
 * @property-read int $suplyCountStockSupplier {virtual}
 * @property-read ArrayHash $supplyInfo {virtual}
 * @property-read bool|null $isVariant {virtual}
 * @property-read bool $isAudioBook {virtual}
 * @property-read string $cacheId {virtual}
 * @property-read CatalogTree|null $mainCategory {virtual}
 * @property-read ICollection<ProductReview> $reviewsPublic {virtual}
 * @property-read array $parameterValueDimensions {virtual}
 * @property-read int|null $damageLevel {virtual}
 * @property-read ICollection<Product> $relatedProducts {virtual}
 * @property-read bool $isCertificate {virtual}
 * @property-read bool $parameterLanguageCountryFlags {virtual}
 *
 * @property-read ProductAvailability $productAvailability {virtual}
 * @property-read bool $syncWithElastic {virtual}
 *
 * @property-read ICollection<ClassEvent> $classEventsPublic {virtual}
 * @property-read ClassEvent $classEventPublicClosest {virtual}
 */
class Product extends BaseEntity implements ParentEntity, Synchronizable
{

	use HasCache, HasTemplateCache, HasCustomFields {
		HasCache::onAfterPersist as hasCacheOnAfterPersist;
		HasCustomFields::onAfterPersist as hasCustomFieldsOnAfterPersist;
		HasTemplateCache::onAfterPersist as hasTemplateCacheOnAfterPersist;
	}
	use HasCustomFields;
	use HasConsts;
	use HasFormDefaultData;
	use HasParameters;

	public const AVAILABILITY_ON_STOCK = 'onStock';
	public const AVAILABILITY_AT_SUPPLIER = 'atSupplier';
	public const AVAILABILITY_PRE_ORDER = 'ask';
	public const AVAILABILITY_NOT_AVAILABLE = 'notAvailable';
	public const AVAILABILITY_ = '';

	public const COURSE_TYPE_ONLINE = 'online';
	public const COURSE_TYPE_ONSITE = 'onsite';
	public const COURSE_TYPE_LIVE = 'live';
	public const COURSE_TYPE_COMBINED = 'combined';

	//private ConfigService $configService;


	private ?Mutation $mutation = null;

	private ProductServices $productServices;

	public function injectService(
		ProductServices $productServices,
	): void
	{
		$this->productServices = $productServices;
	}

	public function getProductServices(): ProductServices
	{
		return $this->productServices;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function hasFreeTransport(): bool
	{
		return $this->loadCache($this->createCacheKey('hasFreeTransport'), function () {
			return $this->hasTag(TagType::transitFree);
		});

	}

	/**
	 * @return ICollection<Tag>
	 * @throws \Throwable
	 */
	public function findActiveTags(int $limit = 3, bool $mutationDependent = false): ICollection
	{
		return $this->loadCache($this->createCacheKey('activeTags', $mutationDependent, $this->getMutation(), $limit), function () use ($limit, $mutationDependent) {
			if ($mutationDependent) {
				return $this->productServices->tagRepository->findActiveTags($this, $this->getMutation(), $limit);
			}
			return $this->productServices->tagRepository->findActiveTags($this, limit: $limit);
		});
	}

	/**
	 * tags on left side of product photo
	 *
	 * @return ICollection<TagLocalization>
	 * @throws \Throwable
	 */
	public function findMainTags(bool $hasLastMinute, int $limit = 3): ICollection
	{
		return $this->loadCache($this->createCacheKey('leftTags', $this->getMutation(), $limit, (int) $hasLastMinute), function () use ($hasLastMinute, $limit) {
			// always search for mutation dependent tags
			$tags = $this->productServices->tagRepository->findActiveTags($this, $this->getMutation(), $limit)->findBy(
				[
					'type!=' => [TagType::videoReview],
				])->fetchAll();

			if ($hasLastMinute) {
				$lastMinuteTag = $this->productServices->tagRepository->getBy(['type' => TagType::lastMinute]);
				if ($lastMinuteTag !== null) {
					$tags[] = $lastMinuteTag;
				}
			}
			$tagIds = array_map(fn(Tag $tag) => $tag->id, $tags);
			return $this->productServices->tagLocalizationRepository->findBy(['tag' => $tagIds, 'mutation' => $this->getMutation()])->orderBy('position')->limitBy($limit);
		});
	}

	protected function getterFirstImage(): ?ProductImage
	{
		return $this->images->toCollection()->fetch();
	}

	public function getTag(TagType $tagType): Tag|null
	{
		return $this->loadCache($this->createCacheKey('tag', $tagType), function () use ($tagType) {

			if ($tagType->isMutationDependTag()) {
				return $this->findActiveTags(mutationDependent: true)->getBy(['type' => $tagType, ]);
			} else {
				return $this->findActiveTags()->getBy(['type' => $tagType]);
			}
		});
	}

	public function hasTag(TagType $tagType): bool
	{
		return $this->getTag($tagType) !== null;
	}


	protected function getterReviewInfo(): array
	{
		if (!isset($this->cache['reviewInfo'])) {
			$cnt = 0;
			$sum = 0;
			$countbyStars = [];
			foreach ($this->reviews as $review) {

				if (!$review->public){
					continue;
				}

				$cnt++;
				$sum += $review->stars;

				if (!isset($countbyStars[$review->stars])) {
					$countbyStars[$review->stars] = 0;
				}

				$countbyStars[$review->stars]++;
			}

			$fractionByStar = [];
			foreach ($countbyStars as $star => $countByStar){
				$fractionByStar[$star] = $cnt > 0 ? (int)round(($countByStar / $cnt) * 100) : 0;
			}

			$this->cache['reviewInfo'] = [
				'count' => $cnt,
				'stars' => $sum,
				'percent' => $cnt > 0 ? round(($sum / $cnt) * 20) : 0,
				'countByStars' => $countbyStars,
				'fractionByStar' => $fractionByStar,
			];
		}

		return $this->cache['reviewInfo'];
	}


	protected function getterCf(): mixed
	{
		return $this->getLocalization(
			$this->getMutation()
		)->cf;
	}


	protected function getterPublic(): int
	{
		return $this->getLocalization(
			$this->getMutation()
		)->public;
	}

	public function isPublic(): bool
	{
		$public = $this->getLocalization($this->getMutation())->public == 1;

		$now = CarbonImmutable::parse($this->productServices->currentDateTimeProvider->getCurrentDateTime());
		$publicTo = CarbonImmutable::parse($this->publicTo);
		$publicFrom = CarbonImmutable::parse($this->publicFrom);

		return $now->greaterThanOrEqualTo($publicFrom) && $now->lessThanOrEqualTo($publicTo) && $public;
	}

	/**
	 * @return IRelationshipCollection<ProductFile>
	 */
	protected function getterFiles(): IRelationshipCollection
	{
		return $this->getLocalization(
			$this->getMutation()
		)->files;
	}






	// ************************************ Price ************************************************


	private function isDateRangeValid(array $valid): bool
	{
		// todo toto zde nema být -> obecná trida a predelat $now
		$now = new DateTimeImmutable();
		/** @var DateTimeImmutable|null $from */
		$from = $valid['from'] ?? null;
		/** @var DateTimeImmutable|null $to */
		$to = $valid['to'] ?? null;

		if ($from !== null && $to !== null) {
			return $now->getTimestamp() >= $from->getTimestamp() && $now->getTimestamp() <= $to->getTimestamp();
		} elseif ($from !== null) {
			return $now->getTimestamp() >= $from->getTimestamp();
		} elseif ($to !== null) {
			return $now->getTimestamp() <= $to->getTimestamp();
		}

		return true;
	}

	public function getPriceInfo(Mutation $mutation, PriceLevel $priceLevel, State $state): PriceInfo
	{
		return $this->loadCache(
			$this->createCacheKey(__FUNCTION__, $mutation, $priceLevel, $state),
			function () use ($mutation, $priceLevel, $state) {
				return $this->productServices->productPriceModel->getPriceInfo($this, $mutation, $priceLevel, $state);
			}
		);
	}




	public function getPriceTag(Mutation $mutation): ?string
	{
		$cf = $this->getLocalization($mutation)->customFieldsJson->product[0]['priceLabel'][0] ?? null;

		if ($cf === null) {
			return null;
		}

		$dateRange = ['from' => null, 'to' => null];

		if (isset($cf['validFrom'])) {
			$dateRange['from'] = new DateTimeImmutable($cf['validFrom']);
		}

		if (isset($cf['validTo'])) {
			$dateRange['to'] = new DateTimeImmutable($cf['validTo']);
		}

		if ($this->isDateRangeValid($dateRange) && isset($cf['name']) && strlen(trim($cf['name'])) > 0) {
			return $cf['name'];
		}

		return null;
	}

	public function priceVatRate(Mutation $mutation, PriceLevel $priceLevel, State $state): BigDecimal
	{
		return $this->getPriceInfo($mutation, $priceLevel, $state)->vatRate;
	}

	public function price(Mutation $mutation, PriceLevel $priceLevel, State $state): Money
	{
		return $this->getPriceInfo($mutation, $priceLevel, $state)->getSellingPrice();
	}

	public function priceVat(Mutation $mutation, PriceLevel $priceLevel, State $state): Money
	{
		return $this->getPriceInfo($mutation, $priceLevel, $state)->getSellingPriceVat();
	}

	public function requalificationPriceVat(Mutation $mutation, State $state): Money
	{
		return $this->getPriceInfo($mutation, $this->productServices->requalificationPriceLevel, $state)->getSellingPriceVat();
	}

	public function getDiscountDate(Mutation $mutation, PriceLevel $priceLevel, State $state): array
	{
		return $this->getPriceInfo($mutation, $priceLevel, $state)->getDiscountDate();
	}


	public function hasPriceFrom(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		return $this->getPriceInfo($mutation, $priceLevel, $state)->hasPriceFrom();
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterPages(): ICollection
	{
		return $this->pagesAll->findBy($this->productServices->treeRepository->getPublicOnlyWhereParams())->findBy(['mutation' => $this->getMutation()]);
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterPagesAll(): ICollection
	{
		return $this->productServices->treeRepository->findTreesInTreeProductRelations($this, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $this->getMutation());
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterPresents(): ICollection
	{
		return $this->presentsAll->findBy($this->productServices->productRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterPresentsAll(): ICollection
	{
		return $this->productServices->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_PRESENT);
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterAccessories(): ICollection
	{
		return $this->accessoriesAll->findBy($this->productServices->productRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterAccessoriesAll(): ICollection
	{
		return $this->productServices->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_ACCESSORY);
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterSimilarProducts(): ICollection
	{
		return $this->similarProductsAll->findBy($this->productServices->productRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterSimilarProductsAll(): ICollection
	{
		return $this->productServices->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_SIMILAR);

	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterProducts(): ICollection
	{
		return $this->productsAll->findBy($this->productServices->productRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<Product>
	 */
	protected function getterProductsAll(): ICollection
	{
		return $this->productServices->productRepository->findAttachedProductsInRelations($this, ProductProduct::TYPE_NORMAL);
	}


	protected function getParamsSorts(): array
	{
		if (!isset($this->cache['getParamsSorts'])) {
			$ret = [];
			foreach ($this->productServices->parameterRepository->findAll()->orderBy(['sort' => 'DESC']) as $i) {
				$ret[$i->id] = $i->sort;
			}

			$this->cache['getParamsSorts'] = $ret;
		}

		return $this->cache['getParamsSorts'];
	}


	/**
	 * @return ICollection<CatalogTree>
	 */
	protected function getterAttachCategories(): ICollection
	{
		return $this->attachCategoriesAll->findBy($this->productServices->treeRepository->getPublicOnlyWhere());
	}

	/**
	 * @return ICollection<CatalogTree>
	 */
	protected function getterAttachCategoriesAll(): ICollection
	{
		/** @var ICollection<CatalogTree> $catalogTrees */
		$catalogTrees = $this->productServices->treeRepository->findBy([
			'productTrees->product->id' => $this->id,
			'type' => Tree::TYPE_CATALOG,
		])->orderBy('productTrees->sort');
		return $catalogTrees;
	}

	protected function getterMainCategory(): ?CatalogTree
	{
		return $this->loadCache($this->createCacheKey('mainCategory', $this), function () {
			$localization = $this->getLocalization(
				$this->getMutation()
			);
			return $this->productServices->catalogTreeModel->getMainCategoryForProductLocalization($localization);
		});

	}

	public function getToolTip(): string
	{
		if ($this->isCourse()) {
			return '';
		}

		return $this->productServices->catalogTreeModel->getProductToolTip($this);
	}

	// ************************************ Stock ************************************************

	public function getterSupplyInfo(): ArrayHash
	{
		if (!isset($this->cache['supplyInfo'])) {
			$supplyInfo = new ArrayHash();
			$supplyInfo->total = 0;
			$supplyInfo->someNotAvailable = false;

			foreach ($this->activeVariants as $variant) {
				$supplyInfo->total += $variant->totalSupplyCount;

				if (!$variant->isInStock) {
					$supplyInfo->someNotAvailable = true;
				}
			}
			$this->cache['supplyInfo'] = $supplyInfo;
		}

		return $this->cache['supplyInfo'];
	}


	protected function getterTotalSupplyCount(): int
	{
		return $this->supplyInfo->total;
	}

	protected function getterSuplyCountStockDefault(): int
	{
		return $this->firstVariant->suplyCountStockDefault ?? 0;
	}

	protected function getterSuplyCountStockSupplier(): int
	{
		return $this->firstVariant->suplyCountStockSupplier ?? 0;
	}

	protected function getterIsInStock(): bool
	{
		return $this->totalSupplyCount > 0;
	}


	/**
	 * @return ICollection<Supply>
	 */
	protected function getterSupplies(): ICollection
	{

		return $this->productServices->supplyRepository->findBy([
			'variant' => $this->activeVariants->fetchPairs(null, 'id'),
		]);
	}


	protected function getterFirstVariant(): ?ProductVariant
	{
		if (!isset($this->cache['firstVariant'])) {
			$this->cache['firstVariant'] = $this->variants->toCollection()->fetch();
		}

		return $this->cache['firstVariant'];
	}


	public function vatRateType(?State $state = null): VatRate
	{
		return $this->loadCache($this->createCacheKey('vatRateType', $state), function () use ($state) {
			$stateId = $state ? $state->id : State::DEFAULT_ID;
			return isset($this->vats->$stateId) ? VatRate::from($this->vats->$stateId) : VatRate::Standard;
		});
	}


	/**
	 * @throws LogicException
	 */
	public function vatRate(?State $state = null): BigDecimal
	{
		$vatRateType = $this->vatRateType($state);

		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRates = $this->productServices->stateModel->getAllVatRatesValues($this->getMutation());
		$rate = $vatRates[$stateId]->get($vatRateType);
		if ($rate === null) {
			throw new LogicException(sprintf('Unknown vatRate %s for Product ID %d, state ID %d', $vatRateType->name, $this->id, $stateId));// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return $rate;
	}


	/**
	 * @return ICollection<Parameter>
	 */
	protected function getterVariantParameters(): ICollection
	{
		return $this->productServices->parameterRepository->findBy([
			'variantParameter' => 1
		]);
	}

	protected function getterHasShellVariant(): bool
	{
		return $this->variants->count() == 1 && $this->variants->toCollection()->fetch()->stringId == ProductVariant::SHELL_STRING_ID;
	}


	protected function getterPath(): array
	{
		if (!isset($this->cache['path'])) {

			$path = [];
			if (!$this->isPersisted()) {
				return $path;
			}

			$mainCategory = $this->mainCategory;
			if ($mainCategory !== null) {
				foreach ($mainCategory->pathItems as $item) {
					if ($item instanceof CatalogTree) {
						$path[] = $item->id;
					}
				}

				$path[] = $mainCategory->id;
			}

			$this->cache['path'] = $path;
		}

		return $this->cache['path'];
	}


	/**
	 * @return ICollection<ProductVariant>
	 */
	protected function getterActiveVariants(): ICollection
	{
		return $this->variants->toCollection()->findBy([
			'variantLocalizations->active' => 1,
			'variantLocalizations->mutation' => $this->getMutation(),
		]);
	}


	protected function getterIsVariant(): bool
	{
		return FALSE;
	}


	protected function getterCacheId(): string
	{
		return 'prod' . $this->id;
	}


	public function setMutation(Mutation $newMutation): void
	{
		try {
			if ($this->getMutation()->id === $newMutation->id) {
				return;
			}
		} catch (MutationNotSetException|MutationIsNotAttachedException $th) {
		}

		$this->flushCache();

		foreach ($this->variants as $variant) {
			$variant->flushCache();
		}

		$this->mutation = $newMutation;
	}


	public function getMutation(): Mutation
	{
		if ($this->mutation !== null) {
			return $this->mutation;
		} else {
			return $this->productServices->mutationHolder->getMutation();
		}
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): ProductLocalization
	{
		if (!isset($this->cache['getLocalization'][$mutation->id])) {
			$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
			assert($localization instanceof ProductLocalization);
			$this->cache['getLocalization'][$mutation->id] = $localization;
		}

		return $this->cache['getLocalization'][$mutation->id];
	}


	protected function getterName(): string
	{
		$name = $this->getLocalization($this->getMutation())->name;
		return $name === null ? '' : $name;
	}


	protected function getterNameAnchor(): string
	{
		$nameAnchor = $this->getLocalization($this->getMutation())->nameAnchor;
		return $nameAnchor === null ? '' : $nameAnchor;
	}


	protected function getterNameTitle(): string
	{
		$nameTitle = $this->getLocalization($this->getMutation())->nameTitle;
		return $nameTitle === null ? '' : $nameTitle;
	}


	protected function getterDescription(): string
	{
		$description = $this->getLocalization($this->getMutation())->description;
		return $description === null ? '' : $description;
	}


	protected function getterKeywords(): string
	{
		$keywords = $this->getLocalization($this->getMutation())->keywords;
		return $keywords === null ? '' : $keywords;
	}


	protected function getterContent(): string
	{
		$content = $this->getLocalization($this->getMutation())->content;
		return $content === null ? '' : $content;
	}


	protected function getterAnnotation(): string
	{
		$annotation = $this->getLocalization($this->getMutation())->annotation;
		return $annotation === null ? '' : $annotation;
	}

	/**
	 * @return ICollection<Review>
	 */
	protected function getterReviewsPublic(): ICollection
	{
		if (!isset($this->cache['reviewsPublic'])) {

			$this->cache['reviewsPublic'] = $this->reviews->toCollection()->findBy(['public' => true]);

		}

		return $this->cache['reviewsPublic'];
	}

	protected function getterInCategories(): array
	{
		if (!isset($this->cache['inCategories'])) {
			$allCats = $this->productServices->catalogTreeModel->getAllCatalogCategories($this, $this->getMutation());
			$inCats = [];
			foreach ($allCats as $cat) {
				if ($cat->level > 1) {
					$inCats[] = $cat;
				}
			}

			$this->cache['inCategories'] = $inCats;
		}

		return $this->cache['inCategories'];
	}


	/**
	 * Kvuli sjednoceni API P a PV (tam, kde v $object muze byt instance obojiho)
	 * @return string
	 */
	public function getterNameVariant(): string
	{
		return '';
	}

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<ProductLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->productLocalizations->toCollection();
	}


	public function getterParameterValueDimensions(): array
	{
		if (!isset($this->cache['parameterValuesDimension'])) {
			$parameterValuesDimension = $this->parametersValues->toCollection()->findBy(['parameter->uid' => [Parameter::UID_HEIGHT, Parameter::UID_WIDTH, Parameter::UID_DEPTH]])->fetchPairs('parameter->uid');
			if (!count($parameterValuesDimension)) {
				return $this->cache['parameterValuesDimension'] = [];
			}

			$parameterValuesDimensionSorted = [];
			foreach ([Parameter::UID_HEIGHT, Parameter::UID_WIDTH, Parameter::UID_DEPTH] as $order) {
				if (!isset($parameterValuesDimension[$order])) {
					continue;
				}
				$parameterValuesDimensionSorted[$order] = $parameterValuesDimension[$order];
			}
			$this->cache['parameterValuesDimension'] = $parameterValuesDimensionSorted;
		}

		return $this->cache['parameterValuesDimension'];
	}

	protected function getterProductAvailability(): ProductAvailability
	{
		if (!isset($this->cache['availability'])) {
			$this->cache['availability'] = $this->productServices->productAvailabilityService->create($this);
		}
		return $this->cache['availability'];
	}

	/**
	 * @return ICollection<ParameterValue>
	 */
	public function getProductComplectation(): ICollection
	{
		if (!isset($this->cache['productComplectation'])) {
			$this->cache['productComplectation'] = $this->productServices
				->parameterValueRepository
				->findValues($this, Parameter::UID_PRODUCT_COMPLECTATION);
		}
		return $this->cache['productComplectation'];
	}

	public function getLanguage(string $separator = ', '): ?string
	{
		return $this->loadCache($this->createCacheKey('languageText'), function () use($separator) {
			$languages = $this->productServices->parameterValueRepository->findValues($this, Parameter::UID_LANGUAGE)->fetchAll();
			if ($languages === []) {
				return null;
			}

			return implode(
				$separator,
				array_map(fn (ParameterValue $language) => $language->internalValue, $languages)
			);
		});
	}

	public function getCourseTypeParameterValue(): ?ParameterValue
	{
		return $this->loadCache($this->createCacheKey('getCourseTypeParameterValue'), function () {
			$classTypes = $this->productServices->parameterValueRepository->findValues($this, Parameter::UID_COURSE_TYPE)->fetchAll();
			if ($classTypes === []) {
				return null;
			}
			return $classTypes[0];
		});
	}

	public function getRequalificationPossibilityParameterValue(): ?ParameterValue
	{
		return $this->loadCache($this->createCacheKey('getRequalificationPossibilityParameterValue'), function () {
			$classTypes = $this->productServices->parameterValueRepository->findValues($this, Parameter::UID_REQUALIFICATION_TYPE)->fetchAll();
			if ($classTypes === []) {
				return null;
			}
			return $classTypes[0];
		});
	}

	public function isOnlineCourse(): bool
	{
		return ($this->isCourse() && $this->getCourseTypeParameterValue()?->internalAlias === self::COURSE_TYPE_ONLINE);
	}

	public function isOnlineCourseWithoutEvent(): bool
	{
		$classEvents = $this->classEvents->toCollection()
			->findBy($this->productServices->classEventRepository->getPublicOnlyWhereParams())
			->findBy([
				'mutation' => $this->getMutation(),
			]);

		$isAvailable = false;
		foreach ($classEvents as $event) {
			if ($event->isAvailable) {
				$isAvailable = true;
			}
		}

		return $this->isOnlineCourse() && $isAvailable === false;
	}


	public function isOnsiteCourse(): bool
	{
		return ($this->isCourse() && $this->getCourseTypeParameterValue()?->internalAlias === self::COURSE_TYPE_ONSITE);
	}

	public function isLiveCourse(): bool
	{
		return ($this->isCourse() && $this->getCourseTypeParameterValue()?->internalAlias === self::COURSE_TYPE_LIVE);
	}

	public function isCombinedCourse(): bool
	{
		return ($this->isCourse() && $this->getCourseTypeParameterValue()?->internalAlias === self::COURSE_TYPE_COMBINED);
	}


	public function getCategoryByLevel(int $level = 0) : ?Tree
	{
		$i = 0;
		foreach ($this->path as $treeId) {
			if ($i === $level) {
				return $this->productServices->treeRepository->getById($treeId);
			}
			$i++;
		}

		return null;
	}

	public function getCategoryForCompare(): ?Tree
	{
		if ($this->mainCategory->cf?->compare?->shouldReverseCategoryPathForCompare ?? false) {
			return $this->mainCategory;
		}

		return $this->getCategoryByLevel(1);
	}

	public function getterDamageLevel(): int|null
	{
		if (!$this->isDamaged) {
			return null;
		}

		return match ($this->damagedType) {
			'Mírně poškozeno' => 1,
			'Středně poškozeno' => 2,
			'Více poškozeno' => 3,
			default => 0,
		};
	}

	public function getterIsAudiobook(): int
	{
		return (int) (Strings::lower($this->itemType ?? '') === 'audiokniha');
	}

	public function getterIsBook(): int
	{
		return (int) (Strings::lower($this->itemType ?? '') === 'kniha');
	}

	public function getterIsElectonicBook(): int
	{
		return (int) (Strings::lower($this->itemType ?? '') === 'e-kniha');
	}

	/**
	 * @return ArrayCollection<Product>
	 */
	public function getterRelatedProducts(): ArrayCollection
	{
		if (!$this->isDamaged) {
			$damagedProducts = $this->damagedProducts->toCollection()->fetchAll();
		} else {
			$damagedProducts = [$this->damagedParent];
		}
		$relatedProducts = $this->productProducts->toCollection()->findBy(['attachedProduct->productType!=' => $this->productType])->fetchPairs(null, 'attachedProduct');

		$repository = $this->productServices->productRepository;
		/** @var array<int, Product> $products */
		$products = array_merge($damagedProducts, $relatedProducts);
		$sortedRelatedEntities = new ArrayCollection(array_values($products), $repository)->orderBy('productType->sort')->orderBy('score', ICollection::DESC)->fetchAll();

		/** @var array<int, Product> $allProducts */
		$allProducts = array_merge($sortedRelatedEntities, $this->productProducts->toCollection()->findBy(['attachedProduct->productType' => $this->productType])->fetchPairs(null, 'attachedProduct'));

		return new ArrayCollection(array_values($allProducts), $repository);
	}

	public function getProductTypeName(): string
	{
		if ($this->isDamaged) {
			//return 'damaged_text_box_'.$this->damageLevel;
		}

		return $this->productType?->name ?? $this->itemType ?? '';
	}

	public function getProductTypeIconName(): string
	{
		return $this->productType->icon;
	}

	public function getGiftDate(Mutation $mutation): ?DateTimeImmutable
	{
		$publicGift = $this->productServices->giftLocalizationRepository->findAllPublicInMutation($this->getMutation(), $mutation->currency->getName())->getBy(['gift->product' => $this]);
		if ($publicGift !== null) {
			return new DateTimeImmutable();
		}
		return $this->giftDate;
	}

	protected function getterIsCertificate(): bool
	{
		if (!$this->isElectronic) {
			return false;
		}

		if (!isset($this->productType)){
			return false;
		}

		if ($this->productType->uid !== ProductType::UID_CERTIFICATE) {
			return false;
		}

		return true;
	}

	/**
	 * @return array<LibraryImage>
	 */
	protected function getterParameterLanguageCountryFlags(): array
	{
		return $this->productServices->flagImageService->findFlags($this);
	}


	public function onAfterPersist(): void
	{
		$this->hasCacheOnAfterPersist();
		$this->hasTemplateCacheOnAfterPersist();
	}

	public function getDeliveryInDays(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): ?int
	{
		if (!$deliveryDate = $this->productAvailability->getDeliveryDate($mutation, $state, $priceLevel, $currency)) {
			return null;
		}

		return $deliveryDate->from->diff(DateTime::from('now')->modify('-1 second'))->days ?: null;
	}

	public function canShowBox(): bool
	{
		return $this->productServices->partCondition->canShow(PartCondition::PART_PRODUCT_BOX);
	}

	public function getterSyncWithElastic(): bool
	{
		if (($this->isOld === 0 && $this->public === 1)) {
			return true;
		}
		return false;
	}

	public function getBanners(): array
	{
		$bnrs = $this->firstVariant->mainCategory?->cf->product_bnrs ?? [];

		$weightedData = $weightedDataLog = [];
		foreach ($bnrs as $k => $bnr) {
			if (!isset($bnr->images)) {
				unset($bnrs[$k]);
				continue;
			}

			$weight = (int) ($bnr->weight ?? 0);
			$bnrs[$k]->weight = $weight;

			$weightedData[$k] = mt_rand() / mt_getrandmax() * $weight;
			$weightedDataLog[$k] = -log(1 - mt_rand() / mt_getrandmax()) * $weight;

		}

		arsort($weightedData);
		arsort($weightedDataLog);

		$sorted = [];
		foreach (array_keys($weightedData) as $key) {
			$sorted[] = $bnrs[$key];
		}

		return $sorted;
	}

	public function isStockedInLastTime(string|DateTimeImmutable $time = '-1 day'): bool
	{
		if (is_string($time)) {
			$time = new DateTimeImmutable($time);
		}
		return $this->supplies->findBy(['amount>' => 0, 'lastOnStock<=' => $time])->countStored() > 0;
	}

	public function isCourse(): bool
	{
		return $this->productType?->uid === ProductType::UID_CLASS;
	}


	public function getLectors(bool $onlyPublicEvents = false): array
	{
		$lectors = [];
		$events = $this->classEvents->toCollection();
		if ($onlyPublicEvents) {
			$events = $events->findBy(['public' => 1]);
		}
		foreach ($events as $classEvent) {
			$lectors += $classEvent->lectors->toCollection()->fetchPairs('id');
		}
		return $lectors;
	}

	public function getSchedule(): ArrayHash
	{
		return $this->loadCache('schedule', function (){
			/** @var array<int, array<string, mixed>> $list */
			$list = [];
			$totalDuration = 0; $i = 0;

			if ($this->classSections->toCollection()->findBy(['type' => 'category'])->countStored() === 0) {
				$this->cache['duration'] = $totalDuration;
				if ($this->classSections->countStored() > 0) {
					trigger_error('Product "' . $this->name . '" has no category section in class sections, but has class sections. This is probably a bad data.', E_USER_NOTICE);
				}
				return ArrayHash::from([]);
			}

			foreach ($this->classSections as $classSection) {
				if ($classSection->type === 'category') {
					$item = [
						'type' => 'category',
						'name' => $classSection->name,
						'duration' => $classSection->completionTime,
						'items' => [],
					];
					$list[] = $item;
					$i = 0;
				} else {
					$sectionItem = [
						'type' => $classSection->type,
						'name' => $classSection->name,
						'duration' => $classSection->completionTime,
					];

					if (!isset($list[count($list) - 1])) {
						// bad data, item without category
						continue;
					}


					$list[count($list) - 1]['duration'] += $classSection->completionTime;
					$list[count($list) - 1]['items'][] = $sectionItem;
					if ($i === 0) {
						$list[count($list) - 1]['tag'] = $classSection->type;
					}
					$i++;
				}
				$totalDuration += $classSection->completionTime;
			}

			$this->cache['duration'] = $totalDuration;
			return ArrayHash::from($list);
		});
	}

	public function getDuration() : int
	{
		if (!isset($this->cache['duration'])) {
			$this->getSchedule();
		}

		return $this->cache['duration'];
	}

	/**
	 * @return ICollection<ClassEvent>
	 */
	protected function getterClassEventsPublic(): ICollection
	{
		return $this->productServices->classEventRepository->findPublicEvents($this, $this->getMutation(), $this->productServices->currentDateTimeProvider->getCurrentDateTime());
	}

	/**
	 * @return ClassEvent
	 */
	public function getterClassEventPublicClosest(): null|ClassEvent
	{
		return $this->classEventsPublic->fetch();
	}



	public function getExternalId(): null|string
	{
		return $this->extId;
	}

	public function setExternalId(null|string|int $externalId): void
	{
		$this->extId = (string) $externalId;
	}

	public function hasPresent(): bool
	{
		if (($this->presents->count() > 0)) {
			return true;
		}
		return false;
	}

	public function hasPromoPrice(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		$discountLimitInPercent = (float) ($this->mainCategory->cf->promoPriceDefinition->percentLimit ?? 0);
		if ($discountLimitInPercent > 0.0) {
			$priceInfo = $this->getPriceInfo($mutation, $priceLevel, $state);
			$discountInPercent = $priceInfo->getOldPriceDiscountPercentage();
			if ($discountInPercent >= $discountLimitInPercent) {
				return true;
			}
		}
		return false;
	}

	public function getTemplateCacheTags(): array
	{
		$tags = [static::class, static::class . '/' . $this->id];
		foreach ($this->variants as $variant) {
			$tags[] = $variant::class . '/' . $variant->id;
		}
		return $tags;
	}

	public function hasBlogsAttachedToProduct(): bool
	{
		return $this->blogLocalizations->count() > 0;
	}

}

<?php

declare(strict_types=1);

namespace App\Model\Orm\PriceLevel;

use App\Model\Orm\ProductVariantPriceLog\ProductVariantPriceLog;
use App\Model\Orm\User\User;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property string $name
 * @property bool $hasValid {default false}
 * @property bool $hasPriceBox {default null}
 * @property bool $isSelectable {default false}
 *
 * RELATIONS
 * @property OneHasMany<ProductVariantPrice> $prices {1:m ProductVariantPrice::$priceLevel, cascade=[persist, remove]}
 * @property OneHasMany<User> $users {1:m User::$priceLevel, cascade=[persist, remove]}
 * @property OneHasMany<ProductVariantPriceLog> $priceLogs {1:m ProductVariantPriceLog::$priceLevel, cascade=[persist, remove]}
 *
 * @property PriceLevel|null $discountPrice                 {1:1 PriceLevel::$selfPriceLevel, isMain=true}
 * @property PriceLevel|null $selfPriceLevel                {1:1 PriceLevel::$discountPrice}
 *
 */
class PriceLevel extends Entity
{

	public const DEFAULT_ID = 1;
	public const DEFAULT_DISCOUNT_ID = 3;
	public const REQUALIFICATION_ID = 4;

	public const TYPE_DEFAULT = 'default';
	public const TYPE_DEFAULT_DISCOUNT = 'default_discount';
	public const TYPE_RECOMMENDED = 'recommended';
	public const TYPE_REFERENCE = 'reference';
	public const TYPE_PURCHASE = 'purchase';

	public const TYPE_REQUALIFICATION = 'requalification';
	public const TYPE_LAST_MINUTE = 'last_minute';

	public const string TYPE_BRONZE = 'bronze';
	public const string TYPE_BRONZE_DISCOUNT = 'bronze_discount';
	public const string TYPE_SILVER = 'silver';
	public const string TYPE_SILVER_DISCOUNT = 'silver_discount';
	public const string TYPE_GOLD = 'gold';
	public const string TYPE_GOLD_DISCOUNT = 'gold_discount';


}

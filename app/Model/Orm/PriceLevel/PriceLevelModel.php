<?php declare(strict_types = 1);

namespace App\Model\Orm\PriceLevel;

use App\Exceptions\LogicException;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserProvider;

class PriceLevelModel
{

	use HasStaticCache;


	public function __construct(
		private readonly PriceLevelRepository $repository,
		private readonly UserProvider $userProvider,
	)
	{
	}

	public function getBasePriceLevel(?User $userEntity): ?PriceLevel
	{
		return $this->getAllPriceLevelByType()[PriceLevel::TYPE_DEFAULT] ?? null;
	}

	public function getAllPriceLevel(): array
	{
		return $this->tryLoadCache($this->createCacheKey('getAllPriceLevel'), function () {
			return $this->repository->findAll()->fetchAll();
		});
	}

	public function getAllPriceLevelByType(array $onlyTypes = []): array
	{
		return $this->tryLoadCache($this->createCacheKey('getAllPriceLevelByType', $onlyTypes), function () use ($onlyTypes) {
			if ($onlyTypes !== []) {
				return $this->repository->findBy(['type' => $onlyTypes])->fetchPairs('type');
			}
			return $this->repository->findAll()->fetchPairs('type');
		});
	}

	public function getByType(string $type): PriceLevel
	{
		$levels = $this->getAllPriceLevelByType([$type]);
		if ($levels === []) {
			throw new LogicException(sprintf('Missing price level of type %s', $type));
		}

		return $levels[$type];
	}

	public function getDefault(): PriceLevel
	{
		if ($this->userProvider->userEntity !== null) {
			return $this->userProvider->userEntity->priceLevel;
		}

		return $this->getByType(PriceLevel::TYPE_DEFAULT);
	}

	public function getById(int $priceLevelId): ?PriceLevel
	{
		return $this->findAll()[$priceLevelId] ?? null;
	}

	public function findAll(): array
	{
		return $this->tryLoadCache($this->createCacheKey('findAll'), function () {
			return $this->repository->findAll()->fetchPairs('id');
		});
	}

}

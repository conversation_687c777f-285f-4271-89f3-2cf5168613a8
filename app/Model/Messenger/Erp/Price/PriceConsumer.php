<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Price;

use App\Exceptions\LogicException;
use App\Model\Currency\CurrencyHelper;
use App\Model\ElasticSearch\All\Facade;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Messenger\Erp\ImportConsumer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use Brick\Math\RoundingMode;
use Brick\Money\Context\CustomContext;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class PriceConsumer extends ImportConsumer
{

	private ?Product $product = null;

	private ?ProductVariant $productVariant = null;

	/** @var PriceLevel[] */
	private array $priceLevels;

	public function __construct(
		protected readonly Facade $esAllFacade,
		protected readonly \App\Model\ElasticSearch\Product\Facade $esProductFacade,
	)
	{
	}

	protected function setup(): void
	{
		$this->priceLevels = $this->orm->priceLevel->findAll()->fetchPairs('type');
	}

	public function __invoke(PriceMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}
	protected function doRemove(): void
	{
		throw new LogicException('Remove message is not implemented yet.');
	}
	protected function doImport(): void
	{
		$this->defaultMutation = $this->orm->mutation->getByIsoCode($this->importCacheOdoo->data->language ?? $this->importCacheOdoo->data->website_id);
		if ($this->defaultMutation === null) {
			throw new SkippedException('Mutation with isoCode "' . ($this->importCacheOdoo->data->language ?? $this->importCacheOdoo->data->website_id) . '" not found.');
		}

		$this->productVariant = $this->orm->productVariant->getBy(['extId' => $this->importCacheOdoo->extId]);
		if ($this->productVariant === null) {
			throw new SkippedException('Product variant not found.');
		}

		$this->product = $this->productVariant->product;
		$prices = [];

		foreach ($this->importCacheOdoo->data->prices as $price) {
			if (!isset($this->priceLevels[$price->discount_type_id])) {
				$this->addWarning(sprintf('Price level "%s" not found.', $price->discount_type_id));
				continue;
			}
			$prices[$price->discount_type_id] = $price;

		}

		$taxAmount = 0;
		$currency = CurrencyHelper::CURRENCY_CZK;
		foreach ($this->priceLevels as $priceLevel) {
			if (isset($prices[$priceLevel->type])) {
				$taxAmount = $prices[$priceLevel->type]->tax_amount;
				$currency = $prices[$priceLevel->type]->currency_id;
				continue;
			}

			$prices[$priceLevel->type] = ArrayHash::from([
				'discount_type_id' => $priceLevel->type,
				'price_unit_untaxed' => 0,
				'price_unit_total' => 0,
				'currency_id' => $currency,
				'date_start' => null,
				'date_end' => null,
				'tax_amount' => $taxAmount,
				'price_box' => null,
			]);

		}

		assert($this->defaultMutation instanceof Mutation);

		foreach ($prices as $p) {
			$priceLevel = $this->priceLevels[$p->discount_type_id];
			$price = $this->orm->productVariantPrice->getBy([
				'mutation' => $this->defaultMutation,
				'productId' => $this->product->id,
				'productVariant' => $this->productVariant,
				'priceLevel' => $priceLevel,
				'price->currency' => $p->currency_id,
			]);

			if ($price === null) {
				$price = new ProductVariantPrice();
				$price->productId = $this->product->id;
				$price->mutation = $this->defaultMutation;
				$price->productVariant = $this->productVariant;
				$price->priceLevel = $priceLevel;
			}

			$price->vat = $p->tax_amount ?? 0;
			$price->price = Price::from(Money::of($p->price_unit_untaxed ?? 0, $p->currency_id, new CustomContext(4), RoundingMode::HALF_UP));

			if ($p->date_start !== '' && $p->date_start !== null) {
				$price->validFrom = new DateTimeImmutable($p->date_start);
			} else {
				$price->validFrom = null;
			}

			if ($p->date_end !== '' && $p->date_end !== null) {
				$price->validTo = new DateTimeImmutable($p->date_end);
			} else {
				$price->validTo = null;
			}

			$priceBox = $p->price_box === false ? null : $p->price_box;

			$price->priceBox = $priceBox;

			$this->orm->productVariantPrice->persistAndFlush($price);
		}

		// TODO: price log
	}

	protected function save(): void
	{
		if ($this->product !== null) {
			$this->product->flushTemplateCache();
			// performance - send update to messenger
			$this->esProductFacade->updateOrDeleteAllMutations($this->product);
			$this->esAllFacade->update($this->product);
		}
		// Do some stuff before importCacheChange persists
		parent::save();
	}

}

<?php declare(strict_types=1);

namespace App\Model\Price;

use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nextras\Dbal\Utils\DateTimeImmutable;

class MoneyWrapper
{

	public bool $differentPrices = false;

	public function __construct(
		public readonly ?int $variantId,
		public readonly Money $money,
		public readonly BigDecimal $vat,
		public readonly ?DateTimeImmutable $from = null,
		public readonly ?DateTimeImmutable $to = null,
		public readonly ?string $priceBox = null,
	)
	{
	}

}

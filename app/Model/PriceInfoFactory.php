<?php
declare(strict_types = 1);

namespace App\Model;


use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use Brick\Math\BigDecimal;
use Brick\Money\Money;

interface PriceInfoFactory
{
	public function create(
		ProductVariant $productVariant,
		Mutation $mutation,
		BigDecimal $vatRate,
		string $currency,
		Money $standardPrice,
		Money $recommendedPrice,
		Money $referencePrice,
		array $discountPricePeriod,
		?Money $discountPrice,
		?string $priceBox = null,
	): PriceInfo;
}

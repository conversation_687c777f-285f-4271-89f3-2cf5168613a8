<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Catalog\Components\DataGrid;

use App\AdminModule\Presenters\Catalog\Components\DataGrid\DataSource\DataMapper;
use App\Model\ElasticSearch\AliasModel;
use App\Model\ElasticSearch\IndexModel;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductType\ProductType;
use App\Model\Orm\User\User;
use App\Model\Translator;
use Elastica\Query;
use Elastica\QueryBuilder;
use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;
use Nette\Application\UI\Control;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Ublaboo\DataGrid\Filter\FilterMultiSelect;
use Ublaboo\DataGrid\Filter\FilterSelect;
use Ublaboo\DataGrid\Utils\Sorting;

class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly AliasModel $aliasModel,
		private readonly User $userEntity,
		private readonly IndexModel $indexModel,
		private readonly Client $esClient,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$csMutation = $this->mutationsHolder->getDefault();
		$esIndex = $this->orm->esIndex->getAllLastActive($csMutation);

		if ($esIndex === null) {
			$grid = new \Ublaboo\DataGrid\DataGrid();
			$grid->setDataSource([]);
			$grid->addColumnText('id', 'id');
			return $grid;
		}

		$b = new QueryBuilder();
		$query = new Query();
		$query->setSize(0);
		$query->addAggregation(
			$b->aggregation()->filter('availability_agg')->setFilter($b->query()->bool())->addAggregation(
				$b->aggregation()->terms('availability_agg')->setField('filter.availability')->setSize(50)->setOrder('_key', 'asc')
			)
		);

		$aggs = $this->indexModel->getIndex($esIndex)->search($query);

		$filterAvailabilityValues = [];
		foreach ($aggs->getAggregation('availability_agg')['availability_agg']['buckets'] as $bucket) {
			$filterAvailabilityValues[$bucket['key']] = $bucket['key'];
		}

		$dataSource = new DataSource\ElasticsearchDataSource(
			$this->esClient, // Elasticsearch\Client
			$this->aliasModel->getAliasName($esIndex),
			$this->getDataMappers(),
		);

		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setStrictSessionFilterValues(false);

		$productTypeFilter = new FilterSelect($grid, 'productType', 'productType', [
			'productType' => ProductType::UID_PRODUCT,
		], 'productType');
		$productTypeFilter = $productTypeFilter->setValue(ProductType::UID_PRODUCT);
		$dataSource->applyFilterSelect($productTypeFilter);

//		$productTypeFilter = new FilterMultiSelect($grid, 'productType', 'productType', [
//			'productType_product' => ProductType::UID_PRODUCT,
//			'productType_cert' => ProductType::UID_CERTIFICATE,
//		], 'productType');
//
//		$productTypeFilter = $productTypeFilter->setValue([ProductType::UID_PRODUCT, ProductType::UID_CERTIFICATE]);
//		$dataSource->applyFilterSelect($productTypeFilter);


		$baseSort = new Sorting(['nameSort' => 'asc']);
		$dataSource->sort($baseSort);

		$grid->setDataSource($dataSource);
		$grid->setItemsPerPageList([30, 50], false);

		if ($this->userEntity->isDeveloper()) {
			$grid->addColumnText('id', 'id')->setSortable()->setFilterText()->setExactSearch();
			$grid->addColumnText('extId', 'extId')->setSortable()->setFilterText()->setExactSearch();
		}

		$grid->addColumnText('erpCode', 'erpCode', 'filter.codes')->setRenderer(function ($value) {
			$codes = $value['filter']['codes'];
			$codes = array_map('strtoupper', $codes);
			return implode(', ', $codes);
		})->setFilterText();

		$grid->addColumnText('ean', 'ean', 'filter.eans')->setRenderer(function ($value) {
			$codes = $value['filter']['eans'];
			return implode(', ', $codes);
		})->setFilterText();

		$grid->addColumnText('nameSort', 'nameSort')->setSortable()->setFilterText();

		$productTypes = $this->orm->productType->findBy(['uid!=' => [ProductType::UID_CLASS]])->fetchPairs('id','name');
		$grid->addColumnText('productType','productType', 'filter.productTypeId')->setRenderer(fn(array $data) => $productTypes[$data['filter']['productTypeId'] ?? 0] ?? '---')->setFilterMultiSelect($productTypes);
		$grid->addColumnText('category', 'category','filter.category')->setRenderer(fn(array $data) => $data['filter']['category'])->setFilterText();
		$grid->addColumnText('score','product_score', 'filter.score')->setRenderer(fn(array $data) => $data['filter']['score'])->setSortable();

		$grid->addColumnText('publish', 'ublaboo_publish', 'filter.publish')
			->setRenderer(function ($data) {
				$parts = [];
				if (isset($data['filter']['publish']) && $data['filter']['publish']) {
					foreach ($data['filter']['publish'] as $publish) {
						if (preg_match('/(.*)_public$/', $publish, $matches)) {
							$parts[] = $matches[1];
						}
					}
				}
				return implode(', ', $parts);
			})
			->setFilterMultiSelect($this->getPublishOptions())->setPrompt('Vše');

		/*$grid->addColumnText('isInStock', 'ublaboo_isInStock', 'filter.isInStock')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isInStock']) && $data['filter']['isInStock']) ? 'Skladem' : 'Vyprodáno';
			})
			->setFilterSelect([
				1 => 'Skladem',
				0 => 'Vyprodáno',
			])->setPrompt('Vše');*/

		$grid->addColumnText('isNew', 'ublaboo_isNew', 'filter.isNew')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isNew']) && $data['filter']['isNew']) ? 'Novinka' : '';
			})
			->setFilterSelect([
				1 => 'Novinky',
				0 => 'Staré',
			])->setPrompt('Vše');

		/*$grid->addColumnText('isOld', 'ublaboo_isOld', 'filter.isOld')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isOld']) && $data['filter']['isOld']) ? 'Prodej ukončen' : '';
			})
			->setFilterSelect([
				1 => 'Prodej ukončen',
				0 => 'Prodávané',
			])->setPrompt('Vše');
*/
		$grid->addColumnText('availability','ublaboo_availability', 'filter.availability')
		     ->setRenderer(fn(array $data) => $data['filter']['availability'])
		     ->setFilterSelect($filterAvailabilityValues)->setPrompt('Vše');

		$grid->addColumnText('isAction', 'ublaboo_isAction', 'filter.isAction')
			->setRenderer(function ($data) {
				return (isset($data['filter']['isAction']) && $data['filter']['isAction']) ? 'Akce' : '';
			})
			->setFilterSelect([
				1 => 'V akci',
				0 => 'Bez akce',
			])->setPrompt('Vše');

		$grid->addColumnDateTime('public', 'public', 'filter.publishDate')
			->setRenderer(function ($data) {
				$parts = [];
				if (isset($data['filter']['publishDate'][0])) {
					$parts[] = (new DateTimeImmutable())->setTimestamp($data['filter']['publishDate'][0])->format('d.m.Y');
				}

				if (isset($data['filter']['publishDate'][1])) {
					$parts[] = (new DateTimeImmutable())->setTimestamp($data['filter']['publishDate'][1])->format('d.m.Y');
				}

				return implode(' - ', $parts);
			})

			->setFilterDateRange('filter.publishDate');

		$grid->addAction('edit', 'Edit', 'Product:edit')->setClass('btn btn-xs btn-primary');

		$grid->setDefaultSort(['nameSort' => 'asc']);

		$grid->setTranslator($this->translator);

		return $grid;
	}

	private function getPublishOptions(): array
	{
		$rows = [];
		foreach ($this->mutationsHolder->findAll(false) as $mutation) {
			$rows[sprintf('%s_public', $mutation->langCode)] = sprintf('%s - publikováno', $mutation->langCode);
			$rows[sprintf('%s_hide', $mutation->langCode)] = sprintf('%s - skryto', $mutation->langCode);

		}

		return $rows;
	}

	private function getDataMappers(): array
	{
		$mappers = [];


		// add boost
		$template['term']['type'] = ['value' => null, 'boost' => 10];
		$mappers[] = new DataMapper(['[term][type][query]' => '[term][type][value]'], $template );

		// {"range":{"filter.publishDate":{"gte":1625349600,"lte":1654379999}}}
		// split and switch
		$mappers[] = new DataMapper([
			'[range][filter.publishDate][gte]' => '[0][range][filter.publishDate][gte]',
			'[range][filter.publishDate][lte]' => '[1][range][filter.publishDate][lte]'
			] );


		// fix old 'should' syntax for ES
		$mappers[] = new DataMapper([
			'[bool][should][0]' => '[bool][should]',
		] );

		return $mappers;
	}

}

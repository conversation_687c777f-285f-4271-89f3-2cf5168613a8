<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters;

use App\AdminModule\Presenters\Catalog\CatalogPresenter;
use App\AdminModule\Presenters\Class\ClassPresenter;
use App\AdminModule\Presenters\ClassEvent\ClassEventPresenter;
use App\AdminModule\Presenters\ClassSection\ClassSectionPresenter;
use App\AdminModule\Presenters\Product\ProductPresenter;
use App\Infrastructure\Latte\Filters;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ProductVariant\Availability\ProductAvailability;
use App\Model\Orm\Routable;
use App\Model\Security\Acl;
use App\Model\Setup;
use App\Model\SetupFactory;
use App\Model\Translator;
use App\Model\TranslatorDB;
use	Nette;
use Nette\DI\Attributes\Inject;

abstract class BasePresenter extends \App\BasePresenter
{
	#[Inject]
	public Translator $translator;

	#[Inject]
	public TranslatorDB $translatorDb;

	#[Inject]
	public MutationHolder $mutationHolder;

	#[Inject]
	public MutationsHolder $mutationsHolder;

	#[Inject]
	public AdminAccessChecker $accessChecker;

	#[Inject]
	public LinkFactory $linkFactory;

	#[Inject]
	public ProductAvailability $productAvailability;
	#[Inject]
	public SetupFactory $setupFactory;

	protected function startup(): void
	{
		$defaultMutation = $this->orm->mutation->getDefault();
		$this->orm->setMutation($defaultMutation);
		$this->mutationHolder->setMutation($defaultMutation);
		$this->orm->setPublicOnly(false);
		$this->translatorDb->setMutation($this->mutationsHolder->getDefaultRs());
		$ipAddress = $this->getHttpRequest()->getRemoteAddress();
		if ($ipAddress !== null && ! $this->accessChecker->isAllowed($ipAddress)) {
			$this->error(httpCode: Nette\Http\Response::S404_NotFound);
		}

		$this->setupFactory->init([$this->productAvailability]);

		parent::startup();

		$this->isLogin();
	}

	protected function createTemplate(?string $class = null): Nette\Application\UI\Template
	{
		$template = parent::createTemplate();
		$template->setTranslator($this->translator);

		return $template;
	}


	protected function beforeRender(): void
	{
		parent::beforeRender();

		// ******* basic ************************
		$this->template->isDeveloper = $this->user->isDeveloper();
		$this->template->config = $this->configService->getParams();
		$this->template->staticFile = 'config/' . $this->configService->getParam('adminAlias');
		$this->template->title = $this->configService->getParam('adminTitle');
		$this->template->pageTabs = $this->configService->get('tabs');
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->translator = $this->translator;
		if (isset($this->object)) {
			$this->template->object = $this->object;
		} else {
			$this->template->object = null;
		}
		$this->template->menu = $this->getMenu();
		$this->template->envName = $this->configService->get('envName');
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);
		$this->template->mutations = $this->orm->mutation->findAll();

		Filters::$mutation = $this->mutationHolder->getMutation();
		Filters::$translator = $this->translator;

		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');

	}

	public function flashMessage(string|\Stringable|\stdClass $message, string $type = "info"): \stdClass
	{
		$message = $this->translator->translate($message);
		return parent::flashMessage($message, $type);
	}

	/**
	 * ověří, zda-li uživatel přihlášen a má právo pro AdminModule
	 * pokud ne, přesměruje na přihlášení
	 *
	 * @throws Nette\Application\AbortException
	 * @throws Nette\Application\ForbiddenRequestException
	 */
	protected function isLogin(): void
	{
		if ($this->presenter->name === 'Admin:Sign') {
			return;
		}

		if ($this->userEntity === null) {
			$this->flashMessage('msg_signet_out_inactive', 'info');
			$this->user->logout(true);
			$this->redirect(':Admin:Sign:default', ['backlink' => $this->storeRequest()]);
		}

		if (!$this->user->isLoggedIn() || !$this->user->isAllowed(Acl::RES_ADMIN)) {
			if ($this->user->getLogoutReason() === Nette\Security\User::LogoutInactivity) {
				$this->flashMessage('msg_signet_out_inactive', 'info');
			} elseif ($this->user->isLoggedIn() && !$this->user->isAllowed(Acl::RES_ADMIN)) {
				$this->flashMessage('msg_access_denied', 'error');
			}

			$this->redirect(':Admin:Sign:default', ['backlink' => $this->storeRequest()]);
		}

		//možná předěláme a bude se volat přímo
		$this->checkPermission($this->presenter->name);
	}


	protected function checkPermission(mixed $resource, mixed $privilege = Nette\Security\Authorizator::All): void
	{
		if (!$this->user->isAllowed($resource, $privilege)) {
			throw new Nette\Application\ForbiddenRequestException();
		}
	}

	/**
	 * vrací hlavní menu
	 */
	protected function getMenu(): array
	{
		$menu = [];

		foreach ($this->configService->get('adminMenu') as $sectionName => $items) {
			foreach ($items as $item) {
				$item['isActive'] =  false;
				if ($this instanceof CatalogPresenter) {
					if ($item['action'] === ':Admin:Catalog:class' && $this->action === 'class') {
						$item['isActive'] = true;
					} elseif ($item['action'] === ':Admin:Catalog:default' && $this->action === 'default') {
						$item['isActive'] = true;
					}
				} elseif ($this instanceof ClassPresenter || $this instanceof ClassEventPresenter || $this instanceof ClassSectionPresenter) {
					if ($item['action'] === ':Admin:Catalog:class') {
						$item['isActive'] = true;
					}
				} elseif ($this instanceof ProductPresenter) {
					if ($item['action'] === ':Admin:Catalog:default' && $this->action === 'edit') {
						$item['isActive'] = true;
					}
				} else {
					$item['isActive'] = $this->getName() == $item['resource'];
				}

				if ($this->user->isAllowed($item['resource'])) {
					$menu[$sectionName][] = $item;
				}
			}
		}

		return $menu;
	}

	public function formatTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$dir = is_dir("$dir/templates") ? $dir : dirname($dir);
		return ["$dir/templates/$this->view.latte"];
	}

	public function formatLayoutTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$layout = $this->layout ?: 'layout';

		return [
			"$dir/templates/@$layout.latte",
			__DIR__ . "/../templates/@$layout.latte",
		];
	}

	public function frontLink(Routable $object, array $args = []): string
	{
		return $this->linkFactory->linkTranslateToNette($object, $args);
	}
}

{*embed $templates.'/part/box/toggle.latte', props=>[
	title: 'Publikace v modifikacích',
	id: 'variant-public-'.$variantId,
	open: true,
	tags: [
		[text: 'Lokalizované']
	]
], templates=>$templates}
	{block content}
		{foreach $mutations as $mutation}
			{var $langCode = $mutation->langCode}
			<div class="js-lang js-lang--{$langCode}">
				{include $templates.'/part/core/checkbox.latte',
					props: [
						input: $form['variantLocalizations'][$mutation->id]['active'],
						label: '<span class="grid-inline"><span class="tag">'.$langCode.'</span> <span>Aktivováno</span></span>',
						dataInp: [
							action: 'change->ProductVariantEdit#edit',
							ProductVariantEdit-target: 'lang',
							lang: $langCode,
						]
					]
				}
			</div>
		{/foreach}
	{/block}
{/embed*}
{var $props = [
	title: 'Ceny, dostupnost, varianty',
	id: 'variants',
	icon: $templates.'/part/icons/coins.svg',
	variant: 'main',
	classes: ['u-mb-xxs'],
	tags: [
		[
			text: 'Lokalizované'
		]
	]
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}


	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Cenové hladiny (ceny bez DPH)',
		open: true,
		id: 'variant-price-'.$variantId,
		tags: [
			[text: 'Lokalizované']
		]
	], templates=>$templates}
		{block content}
			{foreach $mutations as $mutation}
				<div class="u-mb-sm js-lang js-lang--{$mutation->langCode|lower}">
					<div class="tag u-mb-xxs">
						{$mutation->langCode}
					</div>
					<div class="grid">

						<div class="grid" style="margin-left:0;margin-right: 0; margin-bottom: 30px;">
							{var $currency = $mutation->currency->getCurrencyCode()}
									{foreach $priceLevels as $priceLevel}
								<div class="grid__cell size--12-12">
									<div class="grid" style="margin-left:0;margin-right: 0;">
										<div class="grid__cell size--3-12" style="border: 4px solid transparent; border-width: 0 0 5px 5px;">
											{include $templates.'/part/core/inp.latte',
											props: [
												classes: [],
												label: $priceLevel->name,
												classesLabel: ['title'],
												input: $form['variants'][$variantId]['variantPrices'][$mutation->id][$currency][$priceLevel->id]['price'],
												type: 'number',
												prefix: $currency,
												dataInp: [
												ProductVariantEdit-target: 'price',
												action: 'input->ProductVariantEdit#edit',
												lang: $mutation->langCode,
												currency: $currency,
											],
											]}
										</div>
										<div class="grid__cell size--1-12" style="border: 4px solid transparent; border-width: 0 0 5px 5px;">
											{include $templates.'/part/core/inp.latte',
											props: [
												classes: [],
												label: 'TAX',
												classesLabel: ['title'],
												input: $form['variants'][$variantId]['variantPrices'][$mutation->id][$currency][$priceLevel->id]['vat'],
												type: 'number',
												postfix: '%',
												dataInp: [
												ProductVariantEdit-target: 'price',
												action: 'input->ProductVariantEdit#edit',
												lang: $mutation->langCode,
												currency: $currency,
											],
											]}
										</div>
										<div class="grid__cell size--2-12" style="border: 4px solid transparent; border-width: 0 0 5px 5px;">
											{var $priceValue = $form['variants'][$variantId]['variantPrices'][$mutation->id][$currency][$priceLevel->id]['price']->value}
											{var $actualPriceVat = App\Infrastructure\Latte\Filters::formatMoney(App\Model\Orm\Price::from(App\Model\VatCalculator::priceWithVat(Brick\Money\Money::of($priceValue, $currency, new Brick\Money\Context\CustomContext(4)), Brick\Math\BigDecimal::of($variantContainer['variantPrices'][$mutation->id][$currency][$priceLevel->id]['vat']->value)))->asMoney(0))}
											{var $label = 's DPH'}
											{if (float) $priceValue > 0}
												{var $label = 's DPH <span style="color:red">'.$actualPriceVat.'</span>'}
											{/if}
													{include $templates.'/part/core/inp.latte',
											props: [
												classes: [],
												label: $label,
												classesLabel: ['title'],
												input: $form['variants'][$variantId]['variantPrices'][$mutation->id][$currency][$priceLevel->id]['priceVat'],
												type: 'number',
												dataInp: [
												ProductVariantEdit-target: 'price',
												action: 'input->ProductVariantEdit#edit',
												lang: $mutation->langCode,
												currency: $currency,
											],
											]}
										</div>

										<div class="grid__cell size--6-12" style="border: 4px solid transparent; border-width: 0 0 5px 5px;">
											<div class="grid" n:if="$priceLevel->hasValid" style="margin-left:0;margin-right: 0;">
												<div class="grid__cell size--6-12" style="border: 4px solid transparent; border-width: 0 0 5px 5px;">
													{include $templates.'/part/core/inp.latte',
													props: [
														classes: [],
														classesLabel: ['title'],
														input: $form['variants'][$variantId]['variantPrices'][$mutation->id][$currency][$priceLevel->id]['validFrom'],
														type: 'datetime-local',
													]}
												</div>
												<div class="grid__cell size--6-12" style="border: 4px solid transparent; border-width: 0 0 5px 5px;">
													{include $templates.'/part/core/inp.latte',
													props: [
														classes: [],
														classesLabel: ['title'],
														input: $form['variants'][$variantId]['variantPrices'][$mutation->id][$currency][$priceLevel->id]['validTo'],
														type: 'datetime-local',
													]}
												</div>
											</div>
										</div>
									</div>
								</div>

							{/foreach}
						</div>

					</div>
				</div>
			{/foreach}
		{/block}
	{/embed}

	{embed $templates.'/part/box/toggle.latte', props: [ title: 'Historie ceny', open: false, id: 'variant-price-history-'.$variantId], templates: $templates}
		{block content}
			{foreach $historyPriceLevels as $priceLevel}
				<h5>{$priceLevel->name}</h5>
				<table class="table table-striped table-sm">
					<thead>
					<tr>
						<th>{_Date}</th>
						<th>{_selling_price}</th>
						<th>{_product_price}</th>
						<th>{_original_price}</th>
						<th>{_label_discountPercent}</th>
					</tr>
					</thead>
					<tbody>
					<tr n:foreach="$pricesHistory[$priceLevel->id] as $price">
						<td>{$price->createdAt|date:'d. m. Y H:i'}</td>
						<td>{$price->salePrice}</td>
						<td>{$price->origPrice}</td>
						<td>{$price->realOrigPrice}</td>
						<td>{if $price->isInSale}{$price->discount} %{else}-{/if}</td>
					</tr>
					</tbody>
				</table>
			{/foreach}

		{/block}
	{/embed}

	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Sklady',
		open: true,
		id: 'variant-supply-'.$variantId,
	], templates=>$templates}
		{block content}
			{foreach $stocks as $stock}
				<div class="u-mb-sm">
					{var $variant = $product->variants->toCollection()->getById($variantId)}
					{if $variant}
						{capture $variantInfo}
							{_'product_stock_last_import'}:
							{if isset($variant->suppliesByStock[$stock->id]) && $variant->suppliesByStock[$stock->id]->lastImport}{$variant->suppliesByStock[$stock->id]->lastImport|date:'j.n.Y H:i:s'}{else}--{/if}
						{/capture}
					{/if}
					{include $templates.'/part/core/inp.latte', props: [
						classes: ['u-mb-xs'],
						input: $form['variants'][$variantId]['variantSupplies'][$stock->id]['amount'],
						label: $stock->name,
						classesLabel: ['title'],
						prefix: 'ks',
						dataInp: [
							ProductVariantEdit-target: 'supply',
							action: 'input->ProductVariantEdit#edit',
						],
						info: $variant ? $variantInfo : ''
					]}
				</div>
			{/foreach}
		{/block}
	{/embed}

	{if $variantCommonContainer = $form['variants'][$variantId]->getComponent('variantCommon')}
		{formContainer $variantCommonContainer}
			{foreach $variantCommonContainer->getComponents() as $item}
				{if $item->options['type'] == 'text'}
					{include $templates.'/part/core/inp.latte' props: [
						input: $item,
						classesLabel: ['title']
					]}
				{elseif $item->options['type'] == 'checkbox'}
					{include $templates.'/part/core/checkbox.latte',
						props: [
							input: $item
						]
					}
				{elseif $item->options['type'] == 'select'}
					{include $templates.'/part/core/inp.latte' props: [
						input: $item,
						type: 'select',
						classesLabel: ['title']
					]}
				{else}
					<div>
						{label $item /}
						{input $item}
					</div>
				{/if}
			{/foreach}
		{/formContainer}
	{/if}

	{/block}
{/embed}

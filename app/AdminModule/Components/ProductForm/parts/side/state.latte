{default $formId = null}

{embed $templates.'/part/box/std.latte', props=>[
	title=> 'Stav',
]}
	{block content}

		<div class="u-mb-sm">
			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				{include $templates.'/part/core/checkbox.latte',
					props: [
						input: $localizationContainer['public'],
						label: '<span class="grid-inline"><span class="tag">'.strtoupper($langCode).'</span> <strong>Publikováno</strong></span>',
						classes: ['u-mb-xs'],
						formId: $formId,

					]
				}
			{/foreach}
		</div>
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isOld'],
				label: $form['productCommon']['isOld']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isInPrepare'],
				label: $form['productCommon']['isInPrepare']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isFreeTransport'],
				label: $form['productCommon']['isFreeTransport']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isFreeTransportForced'],
				label: $form['productCommon']['isFreeTransportForced']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/inp.latte',
			props: [
				input: $form['productCommon']['freeTransportForcedFrom'],
				type: "date",
				formId: $formId,

			]
		}
		{include $templates.'/part/core/inp.latte',
			props: [
				input: $form['productCommon']['freeTransportForcedTo'],
				type: "date",
				formId: $formId,
			]
		}

		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isElectronic'],
				label: $form['productCommon']['isElectronic']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isDamaged'],
				label: $form['productCommon']['isDamaged']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isNew'],
				label: $form['productCommon']['isNew']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isBestseller'],
				label: $form['productCommon']['isBestseller']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['isTop'],
				label: $form['productCommon']['isTop']->caption,
				classes: ['u-mb-xs'],
				formId: $formId,
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['productCommon']['notSoldSeparately'],
				label: $form['productCommon']['notSoldSeparately']->caption,
				formId: $formId,
			]
		}
	{/block}
{/embed}

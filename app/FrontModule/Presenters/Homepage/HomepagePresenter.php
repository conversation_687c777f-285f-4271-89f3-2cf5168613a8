<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Homepage;


use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Mutation\BrowserMutationDetector;
use App\Model\Orm\Routable;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\Checker\Checker;
use App\PostType\Tag\Model\Checker\CheckerDTO;
use App\PostType\Tag\Model\TagType;
use Exception;
use Nette\DI\Attributes\Inject;

final class HomepagePresenter extends BasePresenter
{
	#[Inject]
	public BrowserMutationDetector $browserMutationDetector;
	#[Inject]
	public Checker $checker;
	public array $orderedProductIds = [];


	public function actionDefault(Tree $object): void
	{
		if ($this->configService->isTagged('prod')) {
			$this->redirectUrl('/dronpro-digitalni-univerzita', 307);
		}
		//$this->detectHpMutation($object);
		$this->setObject($this->mutationHolder->getMutation()->rootPage);
		$this->orderedProductIds = $this->userModel->findOrderedProductIds($this->user);
	}

	public function renderDefault(): void
	{
		$this->template->isHomepage = true;
	}

	public function createComponentProductListNews() : ProductList
	{
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_NEWS, $this->userEntity)
										->setExcludeProductIds($this->orderedProductIds)
		                                ->setLimit(21)
		                                ->setLazyLoad();
	}

	public function createComponentProductListBestsellers() : ProductList
	{
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_BESTSELLERS, $this->userEntity)
										->setExcludeProductIds($this->orderedProductIds)
		                                ->setLimit(21)
		                                ->setLazyLoad();
	}

	public function createComponentProductListUpcomming() : ProductList
	{
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_UPCOMMING, $this->userEntity)
										->setExcludeProductIds($this->orderedProductIds)
		                                ->setLimit(21)
		                                ->setLazyLoad();
	}

	public function createComponentProductListInterested() : ProductList
	{
		return $this->productListInterestedFactory->create($this->currentState, $this->priceLevel, $this->userEntity)->setLazyLoad();
	}


//	private function detectHpMutation(Tree $object): void
//	{
//		$signal = $this->getSignal();
//		if ($signal === null) {
//			$anotherHp = $this->browserMutationDetector->detect($object, $this->mutation);
//			if ($anotherHp instanceof Routable) {
//				$this->redirect($anotherHp, ['mutation' => $anotherHp->getMutation()]);
//			}
//		}
//	}
}

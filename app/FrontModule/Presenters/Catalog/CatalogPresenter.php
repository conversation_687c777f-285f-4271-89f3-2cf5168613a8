<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Catalog;


use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsCallbackBuilder;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\Event\CategoryView;
use App\FrontModule\Components\Categories\Categories;
use App\FrontModule\Components\Categories\CategoriesFactory;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Components\Supercategory\SupercategoryComponent;
use App\FrontModule\Components\Supercategory\SupercategoryComponentFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\HasPagerLimits;
use App\FrontModule\Presenters\Pageable;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\SortCreator;
use App\Model\Link\LinkSeo;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use DateTimeImmutable;
use Nette\Application\Attributes\Persistent;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use stdClass;

/**
 * @method Tree getObject()
 * @property Tree $object
 * @property-read DefaultTemplate $template
 */
class CatalogPresenter extends BasePresenter implements Pageable
{

	use HasCustomContentRenderer;
	use HasPagerLimits;

	private array $filterParams;

	private mixed $cleanFilterParam;

	public int $page = 1;

	private BucketFilterBuilder $bucketFilterBuilder;
	private string $order;

	public function __construct(
		private readonly LinkSeo $linkSeo,
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly CatalogProductsCallbackBuilder $catalogProductsCallbackBuilder,
		private readonly CategoriesFactory $categoriesFactory,
		private readonly CatalogProductsFactory $catalogProductsFactory,
		private readonly SupercategoryComponentFactory $supercategoryComponentFactory,
	)
	{
		parent::__construct();
	}


	protected function startup(): void
	{
		parent::startup();

		$this->template->seolink = false;

		$filterParams = $this->getRequest()->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;
	}


	public function actionDefault(CatalogTree $object, array $filter, string $order = SortCreator::TOP): void
	{
		//is virtual category
		/*if ($object->isVirtualCategory()) {
			throw new BadRequestException('Page not found');
		}*/

		if ($object->getVirtualCategorySystemLink() !== null) {
			$this->redirect($object->getVirtualCategorySystemLink());
		} else if ($object->getVirtualCategoryCustomLink() !== null) {
			$this->redirectUrl($object->getVirtualCategoryCustomLink());
		}

		$this->order = $order;
		$this->setObject($object);

		// je nasatveno presmerovani na jinou kategorii
		if (isset($this->object->cf->redirectToCategory->category) && $this->object->cf->redirectToCategory->category) {
			$this->redirect($this->object->cf->redirectToCategory->category);
		}

		$referralCategory = $object->customFieldsJson->categoryOtherSettings[0]->referalCategory ?? null;
		if ($referralCategory !== null) {
			$this->redirect($this->orm->tree->getById($referralCategory));
		}

		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, $order, $this->filterParams);
	}


	public function renderDefault(CatalogTree $object, string $order = SortCreator::TOP, SeoLinkLocalization|null $seoLink = null): void
	{

		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;

		$filter = $this->bucketFilterBuilder->getFilter();
		$this->seoToolsFilter = $filter;

		$this['seoTools']->setFilter($this->seoToolsFilter);

		$this->seoToolsSort = $this->bucketFilterBuilder->getSort();

		$this->template->filter = $filter;
		$this->template->catalogOrder = $order;

		// 'top' is nearest for courses
		$this->template->sortingOptions = [SortCreator::TOP, SortCreator::CHEAPEST, SortCreator::EXPENSIVE];
		/* if courses vs. products need different sorting
 		$this->template->sortingOptions = $object->hasCourses ?
			[SortCreator::TOP, SortCreator::CHEAPEST, SortCreator::EXPENSIVE] :
			[SortCreator::TOP, SortCreator::CHEAPEST, SortCreator::EXPENSIVE];
		*/

		$this->template->seoLink = $seoLink;
		$this->template->linkSeo = $this->linkSeo;
		$this->template->categories = [];

		$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);
	}


	public function actionVisited(): void
	{
		$this->redirect($this->mutation->pages->eshop);
	}


	protected function beforeRender(): void
	{
		parent::beforeRender();

		$this->template->seolink = false;
		$this->template->isOpenInCookie = function ($name) {
			return $this->getHttpRequest()->getCookie('isOpen' . $name);
		};

		$this->addSeoFilterCatalog();
	}


	/**
	 * Prida do sablony SEO texty z kategorie, ktere se pouzijou pri filtorvani
	 */
	protected function addSeoFilterCatalog(): void
	{
		$seoFilterCatalog = new stdClass();
		$this->template->seoFilterCatalog = $seoFilterCatalog;
	}

	public function createComponentSupercategory() : SupercategoryComponent
	{
		$catalogTree = $this->getObject();
		assert($catalogTree instanceof CatalogTree);
		return $this->supercategoryComponentFactory->create($catalogTree);
	}

	public function createComponentCategories() : Categories
	{
		$catalogTree = $this->getObject();
		assert($catalogTree instanceof CatalogTree);
		return $this->categoriesFactory->create($catalogTree);
	}

	public function createComponentProductListRecommended(): ProductList
	{
		$productIds = $orderedIds = $cartIds =[];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();

			$productIds = $this->userModel->findRelatedOrderedProducts(
				productIds: array_merge($orderedIds, $this->lastVisitedProduct->getProductIds()),
				orderFrom: (new DateTimeImmutable())->modify('-1 year')
			);

		}
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_INTERESTED, $this->userEntity)
										->setIncludeProductIds(array_keys($productIds))
										->setOrderedProductIds($orderedIds)
										->setCartProductIds($cartIds)
										->setAppendBestselling()
										//->setAppendBestsellingWithExclude()
										->addOnBestsellingElasticItemList(function (array &$items): void {
											if(($category = $this->getObject()) !== null){
												$items[] = new IsInPath($category);
											}
										})
										->addOnElasticItemList(function (array &$items): void {
											if(($category = $this->getObject()) !== null){
												$items[] = new IsInPath($category);
											}
										})
		                                ->setLimit(21);
	}

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' =>$this->cleanFilterParam,
		];

		$findCatalogProductsDataCallback = $this->catalogProductsCallbackBuilder->build($this->bucketFilterBuilder);

		$catalogProducts = $this->catalogProductsFactory->create(
			$this->object,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
			'catalog',
			'category',
		);


		$catalogProducts->onAfterRender[] = function($variants) {
			$this->eventDispatcher->dispatch(
				new CategoryView(
					$variants,
					$this->object,
					$this->order,
					$this->filterParams,
				)
			);
		};

		return $catalogProducts;
	}
}

{default $class = false}
{default $c = $object->cf->dronzone_categories ?? []}

<header n:class="b-header-dronzone, $class, u-maw-10-12, u-mx-auto, u-ta-c">
	<img class="b-header-dronzone__bg" src="/static/img/illust/dronzone-bg-landing.webp" alt="" fetchpriority="high">
	<div class="grid">
		<div class="grid__cell size--6-12@lg u-mb-last-0">
			<h1 class="b-header-dronzone__logo">
				<img src="/static/img/illust/dron-zone.svg" alt="{_'dronzone'}" fetchpriority="high" width="494" height="100">
			</h1>
			<div n:if="$object->annotation" class="b-header-dronzone__annot h4 u-mt-0 u-mb-0 u-mb-last-0">
				{$object->annotation|noescape}
			</div>
		</div>
		<div class="grid__cell size--6-12@lg">
			<ul n:if="count($c)" class="b-header-dronzone__list u-maw-4-12 u-mx-auto">
				<li n:foreach="$c as $category"><a href="{plink $category}" class="btn btn--white"><span class="btn__text">{$category->nameAnchor}</span></a></li>
			</ul>
			<form action="?" class="b-header-dronzone__search f-search">
				<p class="f-search__wrap u-mb-0">
					<span class="f-search__inp-fix inp-fix">
						<label for="search" class="u-vhide">{_"form_label_search_dronzone"}</label>
						<input type="search" id="search" name="search" class="f-search__inp inp-text" placeholder="{_'form_label_search_dronzone'}">
					</span>
					<button type="submit" class="f-search__btn btn btn--icon">
						<span class="btn__text">
							{('search')|icon, 'btn__icon'}
							<span class="u-vhide">{_"btn_search"}</span>
						</span>
					</button>
				</p>
			</form>
		</div>
	</div>
</header>
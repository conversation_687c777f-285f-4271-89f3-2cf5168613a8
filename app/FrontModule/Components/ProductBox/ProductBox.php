<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductBox;

use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\AddToCart\AddToCartFactory;
use App\FrontModule\Components\AddToCompareComponent\AddToCompareComponent;
use App\FrontModule\Components\AddToCompareComponent\AddToCompareComponentFactory;
use App\FrontModule\Components\VariantPicker\VariantPickerService;
use App\Model\DTO\Product\ProductDto;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;
use App\Model\TranslatorDB;
use App\PostType\Tag\Model\Checker\Checker;
use App\PostType\Tag\Model\Checker\CheckerDTO;
use App\PostType\Tag\Model\TagType;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class ProductBox extends Control
{

	private ProductDto $productDto;

	protected array $params = [
		'titleTag' => null,
		'class' => '',
		'lazyLoading' => true,
		'longLangs' => false,
	];

	public function __construct(
		private readonly Product $product,
		private readonly ?ProductVariant $variant,
		private readonly Setup $setup,
		private readonly array $parametersToTemplate,
		private readonly AddToCartFactory $addToCartFactory,
		private readonly TranslatorDB $translator,
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly Checker $checker,
		private readonly VariantPickerService $variantPickerService,
		private readonly AddToCompareComponentFactory $addToCompareComponentFactory,
		private readonly ?string $templateFile = null,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	private function init(): void
	{
		$this->productDto = $this->productDtoProvider->get($this->product, $this->variant);
		$this->template->setFile(__DIR__ . '/productBox.latte');
		if ($this->templateFile !== null && file_exists(__DIR__ . '/' . $this->templateFile)) {
			$this->template->setFile(__DIR__ . '/' . $this->templateFile);
		}
	}

	public function render(mixed $param = null): void
	{
		if (is_array($param)) {
			$this->params = array_merge($this->params, $param);
		}

		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->productEntity = $this->product;
		$this->template->productDto = $this->productDto;
		$this->template->productLocalization = $this->product->getLocalization($this->setup->mutation);
		$this->template->priceLevel = $this->setup->priceLevel;
		$this->template->state = $this->setup->state;
		$this->template->mutation = $this->setup->mutation;
		$this->template->lazyLoading = $this->params['lazyLoading'] ?? true;
		$this->template->longLangs = $this->params['longLangs'] ?? false;
		$this->template->class = $this->params['class'] ?? '';
		$this->template->packageContentsKeys = $this->params['packageContentsKeys'] ?? [];
		$this->template->variant = $this->product->firstVariant;
		$this->template->hasVariants = $this->product->activeVariants->count() > 1;
		$this->template->hasLastMinute = false;
		$this->template->parametersToTemplate = $this->parametersToTemplate;

		$this->template->setParameters($this->parametersToTemplate);

		$colorsPickerParameterLists = [];
		if ($this->product->isCourse()) {
			$this->template->hasLastMinute = $this->checker->getCheckerFunction(TagType::lastMinute, $this->setup)(
				new CheckerDTO(productLocalization: $this->product->getLocalization($this->setup->mutation))
			);
		} else {
			if ($this->product->activeVariants->count() > 1) {
				$pickerParameterLists = $this->variantPickerService->getParameterValuesForProduct(
					VariantPickerService::TYPE_PRODUCT_BOX,
					$this->product->getLocalization($this->setup->mutation),
					$this->setup
				);
				$colorsPickerParameterLists = array_values($pickerParameterLists)[0]['items'] ?? [];
				$colorsPickerParameterLists = array_values($colorsPickerParameterLists);
			}
		}
		$this->template->colorsPickerParameterLists = $colorsPickerParameterLists;
		$this->template->setTranslator($this->translator);

		$this->template->render();
	}

	protected function createComponentAddToCart(): AddToCart
	{
		return $this->addToCartFactory->create($this->product, $this->productDto, $this->setup->state, $this->setup->priceLevel);
	}

	public function createComponentAddToCompareComponent(): AddToCompareComponent
	{
		assert($this->product instanceof Product);
		return $this->addToCompareComponentFactory->create($this->product, $this->setup, [
			'from' => 'productBox',
		]);
	}

}

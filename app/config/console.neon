extensions:
    console: Contributte\Console\DI\ConsoleExtension(%consoleMode%)
    console.cache: Contributte\Console\Extra\DI\AdvancedCacheConsoleExtension(%consoleMode%)

console:
    name: Superadmin
    version: '1.0'
    autoExit: false

console.cache:
	generators:
		latte: Contributte\Console\Extra\Cache\Generators\LatteTemplatesCacheGenerator(
			dirs: [%appDir%],
			rootDir: ::realpath(%appDir%/..)
		)

		di: Contributte\Console\Extra\Cache\Generators\DiContainersCacheGenerator(
			[production: [debugMode: false, consoleMode: false]],
			@configurator
		)

	cleaners:
		oldRedisStorage: App\Infrastructure\Cache\RedisCacheCleaner(
			commonPrefix: "%config.projectName%:%stageName%:"
			currentPrefix: %redis.storage.prefix%
		)

services:
	configurator:
		type: Nette\Bootstrap\Configurator
		imported: true
		autowired: false

	- Apitte\Console\Command\RouteDumpCommand
	- App\Console\Dev\ShrinkDbCommand(stageName: %stageName%)
	- App\Console\Dev\BrowsePagesCommand(stageName: %stageName%)
	- App\Console\Youtube\YoutubeVideoCommand(channelId: %config.youtube.channelId%, secretKey: %config.youtube.secretKey%)

search:
	commands:
		in: %appDir%/Console
		extends: Symfony\Component\Console\Command\Command

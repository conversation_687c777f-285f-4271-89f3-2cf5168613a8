@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-product {
	--product-fs: 1.3rem;
	--product-fs-sm: 1.2rem;
	$s: &;
	position: relative;
	z-index: 1;
	display: flex;
	flex-direction: column;
	font-size: var(--product-fs);
	.grid__cell > div:has(&),
	.grid__cell > div > & {
		height: 100%;
	}
	.flag {
		--flag-fs: 1.1rem;
		--flag-h: 2.2rem;
		--flag-padding: 0.2rem 0.8rem 0.1rem;
		--flag-gap: 0.2rem;
	}
	&::before {
		content: '';
		position: absolute;
		top: 0.1rem;
		right: -0.1rem;
		bottom: -0.1rem;
		left: 0;
		z-index: -1;
		border: 0.1rem solid variables.$color-icon-minor;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-white;
		visibility: hidden;
		opacity: 0;
		transition: opacity variables.$t, visibility variables.$t;
	}
	&__img-wrap {
		position: relative;
		margin: 0 0 2rem;
	}
	// &:has(&__category-rating) &__img-wrap {
	// 	margin: 0 0 1rem;
	// }
	&:has(&__variants) &__img-wrap {
		margin: 0 0 3.8rem;
	}
	&__img {
		&--hover {
			position: absolute;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
			inset: 0;
		}
		img {
			border-radius: variables.$border-radius-xl;
		}
	}
	&__flags {
		position: absolute;
		top: -1rem;
		left: -1rem;
	}
	&__badge {
		position: absolute;
		top: -1rem;
		right: -1rem;
	}
	&__type + &__badge {
		top: 3.2rem;
	}
	&__variants {
		--arrow-position: -1.5rem;
		--btn-size: 3rem;
	}
	&__videoreview {
		position: absolute;
		right: 0;
		bottom: 1rem;
		&.flag {
			--flag-icon-size: 1.2rem;
			--flag-gap: 0.4rem;
			--flag-fs: 0.9rem;
			--flag-h: 0.9rem;
			--flag-padding: 0.2rem 0.6rem;
		}
	}
	&__variants + &__videoreview {
		bottom: 4.6rem;
	}
	&__title {
		margin: 0 0 0.8rem;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		@include mixins.line-clamp(2);
		text-decoration: none;
	}
	&__category-rating {
		display: flex;
		gap: 1rem;
		align-items: center;
		margin: 0 0 0.4rem;
	}
	&__category {
		--color-link: #{variables.$color-help};
		--color-hover: #{variables.$color-help};
		--color-link-decoration: transparent;
		font-weight: 700;
		font-size: 1rem;
		text-transform: uppercase;
	}
	&__rating {
		margin-left: auto;
	}
	&__info {
		display: flex;
		gap: 0.4rem;
		flex-direction: column;
		font-size: var(--product-fs-sm);
		& > * {
			margin: 0;
		}
	}
	&__desc {
		@include mixins.line-clamp(4);
		a {
			position: relative;
			z-index: 1;
		}
	}
	&__bottom {
		margin-top: auto;
	}
	&__price-add {
		display: flex;
		gap: 0.8rem;
		align-items: flex-end;
	}
	&__btn {
		--btn-h: 4.4rem;
		--btn-padding: 0.9rem 2.2rem 0.7rem;
	}
	&__dates {
		display: flex;
		gap: 0.4rem;
		& > * {
			margin: 0 0 auto;
			white-space: nowrap;
		}
	}
	&__city {
		text-overflow: ellipsis;
		overflow: hidden;
	}
	&__availability {
		margin: 0 0 1.6rem;
	}

	// HOVERS
	.hoverevents &:hover {
		z-index: 2;
		&::before {
			visibility: visible;
			opacity: 1;
		}
	}
	.hoverevents &:has(&__variants a:hover) &__img--hover {
		visibility: visible;
		opacity: 1;
	}

	// MQ
	@media (config.$md-up) {
		--product-fs: 1.4rem;
		--product-fs-sm: 1.3rem;
	}
}
